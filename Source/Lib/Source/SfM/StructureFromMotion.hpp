// Copyright (c) 2024 Minmin Gong
//

#pragma once

#include <filesystem>
#include <memory>
#include <vector>

#include <glm/mat3x3.hpp>
#include <glm/vec2.hpp>
#include <glm/vec3.hpp>
#include <glm/vec4.hpp>

#include "AIHoloImager/Texture.hpp"
#include "Gpu/GpuSystem.hpp"
#include "Python/PythonSystem.hpp"
#include "Util/BoundingBox.hpp"
#include "Util/Noncopyable.hpp"

namespace AIHoloImager
{
    class StructureFromMotion
    {
        DISALLOW_COPY_AND_ASSIGN(StructureFromMotion);

    public:
        struct View
        {
            Texture image_mask;
            glm::uvec4 roi;

            Texture delighted_image;
            glm::uvec2 delighted_offset;

            uint32_t intrinsic_id;

            glm::dmat3x3 rotation;
            glm::dvec3 center;
        };

        struct PinholeIntrinsic
        {
            uint32_t width;
            uint32_t height;

            glm::dmat3x3 k;
        };

        struct Observation
        {
            uint32_t view_id;
            glm::dvec2 point;
            uint32_t feat_id;
        };

        struct Landmark
        {
            glm::dvec3 point;
            std::vector<Observation> obs;
        };

        struct Result
        {
            std::vector<View> views;
            std::vector<PinholeIntrinsic> intrinsics;

            std::vector<Landmark> structure;
        };

    public:
        StructureFromMotion(const std::filesystem::path& exe_dir, GpuSystem& gpu_system, PythonSystem& python_system);
        StructureFromMotion(StructureFromMotion&& other) noexcept;
        ~StructureFromMotion() noexcept;

        StructureFromMotion& operator=(StructureFromMotion&& other) noexcept;

        Result Process(const std::filesystem::path& input_path, bool sequential, const std::filesystem::path& tmp_dir);

    private:
        class Impl;
        std::unique_ptr<Impl> impl_;
    };

    glm::mat4x4 CalcViewMatrix(const StructureFromMotion::View& view);
    glm::mat4x4 CalcProjMatrix(const StructureFromMotion::PinholeIntrinsic& intrinsic, float near_plane, float far_plane);
    glm::vec2 CalcNearFarPlane(const glm::mat4x4& view_mtx, const Obb& obb);
    glm::vec2 CalcViewportOffset(const StructureFromMotion::PinholeIntrinsic& intrinsic);
} // namespace AIHoloImager
