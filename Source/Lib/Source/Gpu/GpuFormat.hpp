// Copyright (c) 2024 Minmin Gong
//

#pragma once

#include <cstdint>

#include <directx/dxgiformat.h>

namespace AIHoloImager
{
    enum class GpuFormat
    {
        Unknown,

        R8_UNorm,
        RG8_UNorm,
        R<PERSON><PERSON><PERSON>_UNorm,
        R<PERSON><PERSON><PERSON>_UNorm_SRGB,
        B<PERSON><PERSON><PERSON>_UNorm,
        BGRA8_UNorm_SRGB,
        BGRX8_UNorm,
        BGRX8_UNorm_SRGB,

        R16_Uint,
        R16_Sint,
        R16_Float,
        RG16_Uint,
        RG16_Sint,
        RG16_Float,
        RGBA16_Uint,
        RGBA16_Sint,
        RGBA16_Float,

        R32_Uint,
        R32_Sint,
        R32_Float,
        RG32_Uint,
        RG32_Sint,
        RG32_Float,
        RGB32_Uint,
        RGB32_Sint,
        RGB32_Float,
        RGBA32_Uint,
        RGBA32_Sint,
        RGBA32_Float,

        D16_UNorm,
        D24_UNorm_S8_Uint,
        D32_Float,
        D32_Float_S8X24_Uint,

        NV12,
    };

    uint32_t FormatSize(GpuFormat fmt) noexcept;
    uint32_t NumPlanes(GpuFormat fmt) noexcept;

    DXGI_FORMAT ToDxgiFormat(GpuFormat fmt) noexcept;
} // namespace AIHoloImager
