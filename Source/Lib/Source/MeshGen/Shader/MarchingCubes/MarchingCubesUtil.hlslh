// Copyright (c) 2024 Minmin Gong
//

#pragma once

uint32_t3 CalcCoord(uint32_t3 coord, uint32_t size)
{
    return min(coord, size - 1).zyx;
}

uint32_t3 DecomposeCoord(uint32_t index, uint32_t size)
{
    const uint32_t xy = index / size;
    const uint32_t z = index - xy * size;
    const uint32_t x = xy / size;
    const uint32_t y = xy - x * size;
    return uint32_t3(x, y, z);
};

uint32_t CalcCubeIndex(Texture3D<float4> scalar_deformation, uint32_t3 coord, uint32_t size, float isovalue)
{
    const float scalars[] = {
        scalar_deformation[CalcCoord(coord + uint32_t3(0, 0, 0), size)].x,
        scalar_deformation[CalcCoord(coord + uint32_t3(1, 0, 0), size)].x,
        scalar_deformation[CalcCoord(coord + uint32_t3(1, 1, 0), size)].x,
        scalar_deformation[CalcCoord(coord + uint32_t3(0, 1, 0), size)].x,
        scalar_deformation[CalcCoord(coord + uint32_t3(0, 0, 1), size)].x,
        scalar_deformation[CalcCoord(coord + uint32_t3(1, 0, 1), size)].x,
        scalar_deformation[CalcCoord(coord + uint32_t3(1, 1, 1), size)].x,
        scalar_deformation[CalcCoord(coord + uint32_t3(0, 1, 1), size)].x,
    };

    uint32_t cube_index = 0;
    for (uint32_t m = 0; m < sizeof(scalars) / sizeof(scalars[0]); ++m)
    {
        if (scalars[m] <= isovalue)
        {
            cube_index |= 1U << m;
        }
    }

    return cube_index;
}
