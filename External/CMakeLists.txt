if(NOT EXISTS ${GIT_EXECUTABLE})
    unset(GIT_EXECUTABLE CACHE)
endif()

find_package(Git)
if(Git_FOUND)
    set(GIT_EXECUTABLE ${GIT_EXECUTABLE} CACHE INTERNAL "" FORCE)
else()
    message(FATAL_ERROR "Failed to find git.")
endif()
if(NOT EXISTS ${GIT_EXECUTABLE})
    message(FATAL_ERROR "The git doesn't exist.")
endif()

function(CloneExternalLib name url branch shallow_exclude)
    set(external_folder "${CMAKE_CURRENT_SOURCE_DIR}")
    set(external_lib_folder "${external_folder}/${name}")

    if(EXISTS ${external_lib_folder})
        message(STATUS "Removing ${external_lib_folder}...")
        file(REMOVE_RECURSE ${external_lib_folder})
    endif()

    message(STATUS "Cloning ${name}...")
    set(branch_param "")
    set(branch_name_param "")
    if(NOT ("x${branch}" STREQUAL "x"))
        set(branch_param "-b")
        set(branch_name_param "${branch}")
    endif()
    set(shallow_exclude_param "")
    if(NOT ("x${shallow_exclude}" STREQUAL "x"))
        set(shallow_exclude_param "--shallow-exclude=${shallow_exclude}")
    endif()
    execute_process(COMMAND "${GIT_EXECUTABLE}" clone ${url} ${branch_param} ${branch_name_param} ${shallow_exclude_param} "${name}" -n WORKING_DIRECTORY "${external_folder}")
endfunction()

function(CheckoutExternalLib name rev)
    set(external_folder "${CMAKE_CURRENT_SOURCE_DIR}")
    set(external_lib_folder "${external_folder}/${name}")

    execute_process(COMMAND "${GIT_EXECUTABLE}" checkout -q ${rev} WORKING_DIRECTORY ${external_lib_folder} RESULT_VARIABLE checkout_err)
    set(${ARGV2} ${checkout_err} PARENT_SCOPE)
    if(NOT ${checkout_err})
        execute_process(COMMAND "${GIT_EXECUTABLE}" submodule update --init --recursive WORKING_DIRECTORY ${external_lib_folder} RESULT_VARIABLE checkout_err)
        set(${ARGV2} ${checkout_err} PARENT_SCOPE)
    endif()
endfunction()

function(UpdateExternalLib name url rev)
    set(external_folder "${CMAKE_CURRENT_SOURCE_DIR}")
    set(external_lib_folder "${external_folder}/${name}")

    if(EXISTS "${external_lib_folder}/.git")
        set(need_clone FALSE)

        set(cached_rev_file_name "${CMAKE_CURRENT_BINARY_DIR}/AIHI_${name}_REV")
        if(EXISTS "${cached_rev_file_name}")
            file(STRINGS "${cached_rev_file_name}" cached_rev)
        else()
            set(cached_rev "")
        endif()
        IF("${cached_rev}" STREQUAL "${rev}")
            set(need_checkout FALSE)
        else()
            message(STATUS "Updating ${name} to revision ${rev}...")
            execute_process(COMMAND "${GIT_EXECUTABLE}" "fetch" "origin" WORKING_DIRECTORY ${external_lib_folder})
            execute_process(COMMAND "${GIT_EXECUTABLE}" "rev-parse" "HEAD" WORKING_DIRECTORY ${external_lib_folder} OUTPUT_VARIABLE head_rev)
            string(STRIP ${head_rev} head_rev)
            if (${head_rev} STREQUAL ${rev})
                set(need_checkout FALSE)
            else()
                set(need_checkout TRUE)
            endif()
            file(WRITE "${cached_rev_file_name}" ${rev})
        endif()
    else()
        set(need_clone TRUE)
    endif()

    if(need_clone)
        CloneExternalLib(${name} ${url} "${ARGV3}" "${ARGV4}")
        set(need_checkout TRUE)
    endif()

    if(need_checkout)
        message(STATUS "Checking out to revision ${rev}...")
        CheckoutExternalLib(${name} ${rev} checkout_err)
        if(checkout_err)
            message(STATUS "COULD NOT checkout revision ${rev}, reclone the repository.")
            CloneExternalLib(${name} ${url} "${ARGV3}" "${ARGV4}")

            message(STATUS "Checking out to revision ${rev}...")
            CheckoutExternalLib(${name} ${rev})
        endif()
    endif()

    set(${ARGV5} ${need_checkout} PARENT_SCOPE)
endfunction()

function(ApplyPatch name patch)
    set(external_folder "${CMAKE_CURRENT_SOURCE_DIR}")
    set(external_lib_folder "${external_folder}/${name}")

    execute_process(COMMAND "${GIT_EXECUTABLE}" "apply" "--check" "--ignore-space-change" "${patch}" WORKING_DIRECTORY ${external_lib_folder} RESULT_VARIABLE checkout_err)
    if(checkout_err)
        message(FATAL_ERROR "Fail to apply patch ${patch}. It's very likely your git is not correctly configured.")
    else()
        message(STATUS "Applying ${patch}...")
        execute_process(COMMAND "${GIT_EXECUTABLE}" "am" "--ignore-space-change" "${patch}" WORKING_DIRECTORY ${external_lib_folder})
    endif()
endfunction()

function(PullLfsFile name path)
    set(external_lib_folder "${CMAKE_CURRENT_SOURCE_DIR}/${name}")
    message(STATUS "Pulling ${path}...")
    execute_process(COMMAND "${GIT_EXECUTABLE}" "lfs" "pull" "--include" "${path}" WORKING_DIRECTORY ${external_lib_folder} RESULT_VARIABLE pull_err)
    if(pull_err)
        message(FATAL_ERROR "COULD NOT pull the LFS file ${path}, error: ${pull_err}.")
    endif()
endfunction()

function(DownloadFile url local_path)
    if(NOT EXISTS ${local_path})
        get_filename_component(file_name ${local_path} NAME)
        message(STATUS "Downloading ${file_name}...")
        file(DOWNLOAD ${url} ${local_path} SHOW_PROGRESS)
    endif()
endfunction()

function(get_all_targets var)
    set(targets)
    get_all_targets_recursive(targets ${CMAKE_CURRENT_SOURCE_DIR})
    set(${var} ${targets} PARENT_SCOPE)
endfunction()

macro(get_all_targets_recursive targets dir)
    get_property(subdirectories DIRECTORY ${dir} PROPERTY SUBDIRECTORIES)
    foreach(subdir ${subdirectories})
        get_all_targets_recursive(${targets} ${subdir})
    endforeach()

    get_property(current_targets DIRECTORY ${dir} PROPERTY BUILDSYSTEM_TARGETS)
    list(APPEND ${targets} ${current_targets})
endmacro()

add_subdirectory(zlib)

add_subdirectory(assimp)
add_subdirectory(cxxopts)
add_subdirectory(dinov2)
add_subdirectory(DirectX-Headers)
add_subdirectory(glm)
add_subdirectory(Intrinsic)
add_subdirectory(openMVG)
add_subdirectory(openMVS)
add_subdirectory(PythonVenv)
add_subdirectory(rembg)
add_subdirectory(stb)
add_subdirectory(TRELLIS)
add_subdirectory(xatlas)
