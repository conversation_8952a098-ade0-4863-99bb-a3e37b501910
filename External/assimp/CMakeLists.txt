UpdateExternalLib("assimp" "https://github.com/assimp/assimp.git" "53b5dba4df171373fddb5ce7f00db6399c0aeeaa" "master" "v5.0.0")

set(BUILD_SHARED_LIBS ON CACHE BOOL "" FORCE)
set(BUILD_TESTING OFF CACHE BOOL "" FORCE)
set(ASSIMP_OPT_BUILD_PACKAGES OFF CACHE BOOL "" FORCE)
set(ASSIMP_ANDROID_JNIIOSYSTEM OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_ALL_IMPORTERS_BY_DEFAULT OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_PLY_IMPORTER ON CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_GLTF_IMPORTER ON CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_ALL_EXPORTERS_BY_DEFAULT OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_PLY_EXPORTER ON CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_GLTF_EXPORTER ON CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_OBJ_EXPORTER ON CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_ASSIMP_TOOLS OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_ASSIMP_VIEW OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_DOCS OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_NONFREE_C4D_IMPORTER OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_SAMPLES OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_TESTS OFF CACHE BOOL "" FORCE)
set(ASSIMP_BUILD_ZLIB OFF CACHE BOOL "" FORCE)
set(ASSIMP_IGNORE_GIT_HASH ON CACHE BOOL "" FORCE)
set(ASSIMP_INSTALL OFF CACHE BOOL "" FORCE)
set(ASSIMP_INSTALL_PDB OFF CACHE BOOL "" FORCE)
set(ASSIMP_NO_EXPORT OFF CACHE BOOL "" FORCE)
set(ASSIMP_RAPIDJSON_NO_MEMBER_ITERATOR ON CACHE BOOL "" FORCE)
unset(ASSIMP_RUNTIME_OUTPUT_DIRECTORY CACHE)
unset(ASSIMP_LIBRARY_OUTPUT_DIRECTORY CACHE)
unset(ASSIMP_ARCHIVE_OUTPUT_DIRECTORY CACHE)

add_subdirectory(assimp EXCLUDE_FROM_ALL)

if(ai_holo_imager_compiler_msvc)
    target_compile_options(assimp
        PRIVATE
            /wd4756 # Ignore the INFINITY constant overflow in some specific Windows SDKs
    )
elseif(ai_holo_imager_compiler_clangcl)
	target_compile_options(assimp
		PRIVATE
			-Wno-unused-function
	)
	if (ai_holo_imager_compiler_version GREATER_EQUAL 160)
		target_compile_options(assimp
			PRIVATE
				-Wno-deprecated-copy
		)
	endif()
endif()

set_target_properties(assimp PROPERTIES FOLDER "External")
