From 47e4bd36fc59334d4a831f9f50644915f8a26a92 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Mon, 17 Jun 2024 22:58:51 -0700
Subject: [PATCH 3/4] [Exif] Code improvements to Exif_IO_EasyExif

* Add a destructor to Exif_IO_EasyExif so it can be used as a stack-based object.
* Move bHaveExifInfo_ into EXIFInfoImpl.
* Simplify (*pimpl_).get() to pimpl_->get().
---
 src/openMVG/exif/exif_IO_EasyExif.cpp         | 133 +++++++++---------
 src/openMVG/exif/exif_IO_EasyExif.hpp         |   8 +-
 .../registration_to_exif_gps_position.cpp     |  17 +--
 .../SfM/main_SfMInit_ImageListing.cpp         |  55 ++++----
 4 files changed, 106 insertions(+), 107 deletions(-)

diff --git a/src/openMVG/exif/exif_IO_EasyExif.cpp b/src/openMVG/exif/exif_IO_EasyExif.cpp
index 60b3ed4e..a3216ae8 100644
--- a/src/openMVG/exif/exif_IO_EasyExif.cpp
+++ b/src/openMVG/exif/exif_IO_EasyExif.cpp
@@ -41,24 +41,29 @@ inline std::string trim_copy( const std::string& s )
 }
 
 class Exif_IO_EasyExif::EXIFInfoImpl {
-  easyexif::EXIFInfo exif_info;
+  friend Exif_IO_EasyExif;
+
  public:
-  easyexif::EXIFInfo& get() {return exif_info;}
+  easyexif::EXIFInfo& get() {return exif_info_;}
+
+ private:
+  easyexif::EXIFInfo exif_info_;
+  bool bHaveExifInfo_ = false;
 };
 
 Exif_IO_EasyExif::Exif_IO_EasyExif():
-  bHaveExifInfo_( false ),
   pimpl_(new Exif_IO_EasyExif::EXIFInfoImpl())
 {
 }
 
 Exif_IO_EasyExif::Exif_IO_EasyExif( const std::string & sFileName ):
-  bHaveExifInfo_( false ),
   pimpl_(new Exif_IO_EasyExif::EXIFInfoImpl())
 {
   open( sFileName );
 }
 
+Exif_IO_EasyExif::~Exif_IO_EasyExif() = default;
+
 bool Exif_IO_EasyExif::open( const std::string & sFileName )
 {
   // Read the file into a buffer
@@ -79,127 +84,127 @@ bool Exif_IO_EasyExif::open( const std::string & sFileName )
   fclose( fp );
 
   // Parse EXIF
-  bHaveExifInfo_ = ( (*pimpl_).get().parseFrom( &buf[0], fsize ) == PARSE_EXIF_SUCCESS );
+  pimpl_->bHaveExifInfo_ = ( pimpl_->get().parseFrom( &buf[0], fsize ) == PARSE_EXIF_SUCCESS );
 
-  return bHaveExifInfo_;
+  return pimpl_->bHaveExifInfo_;
 }
 
 size_t Exif_IO_EasyExif::getWidth() const
 {
-  return (*pimpl_).get().ImageWidth;
+  return pimpl_->get().ImageWidth;
 }
 
 size_t Exif_IO_EasyExif::getHeight() const
 {
-  return (*pimpl_).get().ImageHeight;
+  return pimpl_->get().ImageHeight;
 }
 
 float Exif_IO_EasyExif::getFocal() const
 {
-  return static_cast<float>( (*pimpl_).get().FocalLength );
+  return static_cast<float>( pimpl_->get().FocalLength );
 }
 
 float Exif_IO_EasyExif::getFocalLengthIn35mm() const
 {
-  return static_cast<float>( (*pimpl_).get().FocalLengthIn35mm );
+  return static_cast<float>( pimpl_->get().FocalLengthIn35mm );
 }
 
 float Exif_IO_EasyExif::getFocalPlaneXResolution() const
 {
-  return static_cast<float>( (*pimpl_).get().LensInfo.FocalPlaneXResolution );
+  return static_cast<float>( pimpl_->get().LensInfo.FocalPlaneXResolution );
 }
 
 float Exif_IO_EasyExif::getFocalPlaneYResolution() const
 {
-  return static_cast<float>( (*pimpl_).get().LensInfo.FocalPlaneYResolution );
+  return static_cast<float>( pimpl_->get().LensInfo.FocalPlaneYResolution );
 }
 
 int Exif_IO_EasyExif::getFocalPlaneResolutionUnit() const
 {
-  return static_cast<int>( (*pimpl_).get().LensInfo.FocalPlaneResolutionUnit );
+  return static_cast<int>( pimpl_->get().LensInfo.FocalPlaneResolutionUnit );
 }
 
 std::string Exif_IO_EasyExif::getBrand() const
 {
-  return trim_copy( (*pimpl_).get().Make );
+  return trim_copy( pimpl_->get().Make );
 }
 
 std::string Exif_IO_EasyExif::getModel() const
 {
-  return trim_copy( (*pimpl_).get().Model );
+  return trim_copy( pimpl_->get().Model );
 }
 
 std::string Exif_IO_EasyExif::getLensModel() const
 {
-  return trim_copy( (*pimpl_).get().LensInfo.Model );
+  return trim_copy( pimpl_->get().LensInfo.Model );
 }
 
 std::string Exif_IO_EasyExif::getImageUniqueID() const
 {
-  return (*pimpl_).get().ImageUniqueID;
+  return pimpl_->get().ImageUniqueID;
 }
 
 bool Exif_IO_EasyExif::doesHaveExifInfo() const
 {
-  return bHaveExifInfo_;
+  return pimpl_->bHaveExifInfo_;
 }
 
 std::string Exif_IO_EasyExif::allExifData() const
 {
   std::ostringstream os;
   os
-      << "Camera make       : " << (*pimpl_).get().Make << "\n"
-      << "Camera model      : " << (*pimpl_).get().Model << "\n"
-      << "Software          : " << (*pimpl_).get().Software << "\n"
-      << "Bits per sample   : " << (*pimpl_).get().BitsPerSample << "\n"
-      << "Image width       : " << (*pimpl_).get().ImageWidth << "\n"
-      << "Image height      : " << (*pimpl_).get().ImageHeight << "\n"
-      << "Image description : " << (*pimpl_).get().ImageDescription << "\n"
-      << "Image orientation : " << (*pimpl_).get().Orientation << "\n"
-      << "Image copyright   : " << (*pimpl_).get().Copyright << "\n"
-      << "Image date/time   : " << (*pimpl_).get().DateTime << "\n"
-      << "Original date/time: " << (*pimpl_).get().DateTimeOriginal << "\n"
-      << "Digitize date/time: " << (*pimpl_).get().DateTimeDigitized << "\n"
-      << "Subsecond time    : " << (*pimpl_).get().SubSecTimeOriginal << "\n"
-      << "Exposure time     : 1/" << ( unsigned ) ( 1.0 / (*pimpl_).get().ExposureTime ) << "\n"
-      << "F-stop            : " << (*pimpl_).get().FNumber << "\n"
-      << "ISO speed         : " << (*pimpl_).get().ISOSpeedRatings << "\n"
-      << "Subject distance  : " << (*pimpl_).get().SubjectDistance << "\n"
-      << "Exposure bias     : EV" << (*pimpl_).get().ExposureBiasValue << "\n"
-      << "Flash used?       : " << (*pimpl_).get().Flash << "\n"
-      << "Metering mode     : " << (*pimpl_).get().MeteringMode << "\n"
-      << "Lens focal length : mm\n" << (*pimpl_).get().FocalLength << "\n"
-      << "35mm focal length : mm\n" << (*pimpl_).get().FocalLengthIn35mm << "\n"
+      << "Camera make       : " << pimpl_->get().Make << "\n"
+      << "Camera model      : " << pimpl_->get().Model << "\n"
+      << "Software          : " << pimpl_->get().Software << "\n"
+      << "Bits per sample   : " << pimpl_->get().BitsPerSample << "\n"
+      << "Image width       : " << pimpl_->get().ImageWidth << "\n"
+      << "Image height      : " << pimpl_->get().ImageHeight << "\n"
+      << "Image description : " << pimpl_->get().ImageDescription << "\n"
+      << "Image orientation : " << pimpl_->get().Orientation << "\n"
+      << "Image copyright   : " << pimpl_->get().Copyright << "\n"
+      << "Image date/time   : " << pimpl_->get().DateTime << "\n"
+      << "Original date/time: " << pimpl_->get().DateTimeOriginal << "\n"
+      << "Digitize date/time: " << pimpl_->get().DateTimeDigitized << "\n"
+      << "Subsecond time    : " << pimpl_->get().SubSecTimeOriginal << "\n"
+      << "Exposure time     : 1/" << ( unsigned ) ( 1.0 / pimpl_->get().ExposureTime ) << "\n"
+      << "F-stop            : " << pimpl_->get().FNumber << "\n"
+      << "ISO speed         : " << pimpl_->get().ISOSpeedRatings << "\n"
+      << "Subject distance  : " << pimpl_->get().SubjectDistance << "\n"
+      << "Exposure bias     : EV" << pimpl_->get().ExposureBiasValue << "\n"
+      << "Flash used?       : " << pimpl_->get().Flash << "\n"
+      << "Metering mode     : " << pimpl_->get().MeteringMode << "\n"
+      << "Lens focal length : mm\n" << pimpl_->get().FocalLength << "\n"
+      << "35mm focal length : mm\n" << pimpl_->get().FocalLengthIn35mm << "\n"
       << "GPS Latitude      : deg ( deg, min, sec )\n" << "("
-      <<  (*pimpl_).get().GeoLocation.Latitude << ", "
-      <<  (*pimpl_).get().GeoLocation.LatComponents.degrees << ", "
-      <<  (*pimpl_).get().GeoLocation.LatComponents.minutes << ", "
-      <<  (*pimpl_).get().GeoLocation.LatComponents.seconds << ", "
-      <<  (*pimpl_).get().GeoLocation.LatComponents.direction << ")" << "\n"
+      <<  pimpl_->get().GeoLocation.Latitude << ", "
+      <<  pimpl_->get().GeoLocation.LatComponents.degrees << ", "
+      <<  pimpl_->get().GeoLocation.LatComponents.minutes << ", "
+      <<  pimpl_->get().GeoLocation.LatComponents.seconds << ", "
+      <<  pimpl_->get().GeoLocation.LatComponents.direction << ")" << "\n"
       << "GPS Longitude      : deg ( deg, min, sec )\n" << "("
-      <<  (*pimpl_).get().GeoLocation.Longitude << ", "
-      <<  (*pimpl_).get().GeoLocation.LonComponents.degrees << ", "
-      <<  (*pimpl_).get().GeoLocation.LonComponents.minutes << ", "
-      <<  (*pimpl_).get().GeoLocation.LonComponents.seconds << ", "
-      <<  (*pimpl_).get().GeoLocation.LonComponents.direction << ")" << "\n"
-      << "GPS Altitude       : m" << (*pimpl_).get().GeoLocation.Altitude << "\n"
+      <<  pimpl_->get().GeoLocation.Longitude << ", "
+      <<  pimpl_->get().GeoLocation.LonComponents.degrees << ", "
+      <<  pimpl_->get().GeoLocation.LonComponents.minutes << ", "
+      <<  pimpl_->get().GeoLocation.LonComponents.seconds << ", "
+      <<  pimpl_->get().GeoLocation.LonComponents.direction << ")" << "\n"
+      << "GPS Altitude       : m" << pimpl_->get().GeoLocation.Altitude << "\n"
       << "Lens stop (min, max) : " << "("
-      << (*pimpl_).get().LensInfo.FStopMin << ", "
-      << (*pimpl_).get().LensInfo.FStopMax << ")"
+      << pimpl_->get().LensInfo.FStopMin << ", "
+      << pimpl_->get().LensInfo.FStopMax << ")"
       << "Lens focal (min, max) : " << "("
-      << (*pimpl_).get().LensInfo.FocalLengthMin << ", "
-      << (*pimpl_).get().LensInfo.FocalLengthMax << ")"
-      << "Lens make : " <<  (*pimpl_).get().LensInfo.Make << "\n"
-      << "Lens model : " << (*pimpl_).get().LensInfo.Model << "\n"
-      << "Image Unique ID    : " << (*pimpl_).get().ImageUniqueID << "\n";
+      << pimpl_->get().LensInfo.FocalLengthMin << ", "
+      << pimpl_->get().LensInfo.FocalLengthMax << ")"
+      << "Lens make : " <<  pimpl_->get().LensInfo.Make << "\n"
+      << "Lens model : " << pimpl_->get().LensInfo.Model << "\n"
+      << "Image Unique ID    : " << pimpl_->get().ImageUniqueID << "\n";
   return os.str();
 }
 
 bool Exif_IO_EasyExif::GPSLatitude(double * latitude) const
 {
-  if ((*pimpl_).get().GeoLocation.Latitude != std::numeric_limits<double>::infinity())
+  if (pimpl_->get().GeoLocation.Latitude != std::numeric_limits<double>::infinity())
   {
-    (*latitude) = (*pimpl_).get().GeoLocation.Latitude;
+    (*latitude) = pimpl_->get().GeoLocation.Latitude;
     return true;
   }
   return false;
@@ -207,9 +212,9 @@ bool Exif_IO_EasyExif::GPSLatitude(double * latitude) const
 
 bool Exif_IO_EasyExif::GPSLongitude(double * longitude) const
 {
-  if ((*pimpl_).get().GeoLocation.Longitude != std::numeric_limits<double>::infinity())
+  if (pimpl_->get().GeoLocation.Longitude != std::numeric_limits<double>::infinity())
   {
-    (*longitude) = (*pimpl_).get().GeoLocation.Longitude;
+    (*longitude) = pimpl_->get().GeoLocation.Longitude;
     return true;
   }
   return false;
@@ -217,9 +222,9 @@ bool Exif_IO_EasyExif::GPSLongitude(double * longitude) const
 
 bool Exif_IO_EasyExif::GPSAltitude(double * altitude) const
 {
-  if ((*pimpl_).get().GeoLocation.Altitude != std::numeric_limits<double>::infinity())
+  if (pimpl_->get().GeoLocation.Altitude != std::numeric_limits<double>::infinity())
   {
-    (*altitude) = (*pimpl_).get().GeoLocation.Altitude;
+    (*altitude) = pimpl_->get().GeoLocation.Altitude;
     return true;
   }
   return false;
diff --git a/src/openMVG/exif/exif_IO_EasyExif.hpp b/src/openMVG/exif/exif_IO_EasyExif.hpp
index c27a543f..e1d1d2ce 100644
--- a/src/openMVG/exif/exif_IO_EasyExif.hpp
+++ b/src/openMVG/exif/exif_IO_EasyExif.hpp
@@ -37,6 +37,11 @@ class Exif_IO_EasyExif : public Exif_IO
     */
     explicit Exif_IO_EasyExif( const std::string & sFileName );
 
+    /**
+    * @brief Destructor
+    */
+    ~Exif_IO_EasyExif() override;
+
     /**
     * @brief Open and populate EXIF data
     * @param sFileName path of the image to analyze
@@ -152,9 +157,6 @@ class Exif_IO_EasyExif : public Exif_IO
     /// Hide the easyexif::EXIFInfo to a Pimp "Pointer to implementation".
     class EXIFInfoImpl;
     std::unique_ptr<EXIFInfoImpl> pimpl_;
-
-    /// Indicate if exifInfo_ is populated
-    bool bHaveExifInfo_;
 };
 
 } // namespace exif
diff --git a/src/software/Geodesy/registration_to_exif_gps_position.cpp b/src/software/Geodesy/registration_to_exif_gps_position.cpp
index cbf1ed45..fb22054b 100644
--- a/src/software/Geodesy/registration_to_exif_gps_position.cpp
+++ b/src/software/Geodesy/registration_to_exif_gps_position.cpp
@@ -97,12 +97,7 @@ int main(int argc, char **argv)
   }
 
   // Init the EXIF reader (will be used for GPS data reading)
-  std::unique_ptr<Exif_IO> exifReader(new Exif_IO_EasyExif);
-  if (!exifReader)
-  {
-    std::cerr << "Cannot instantiate the EXIF metadata reader." << std::endl;
-    return EXIT_FAILURE;
-  }
+  Exif_IO_EasyExif exifReader;
 
   // List corresponding poses (SfM - GPS)
   std::vector<Vec3> vec_sfm_center, vec_gps_center;
@@ -116,15 +111,15 @@ int main(int argc, char **argv)
       stlplus::create_filespec(sfm_data.s_root_path, view_it.second->s_Img_path);
 
     // Try to parse EXIF metada & check existence of EXIF data
-    if (! (exifReader->open( view_filename ) &&
-           exifReader->doesHaveExifInfo()) )
+    if (! (exifReader.open( view_filename ) &&
+           exifReader.doesHaveExifInfo()) )
       continue;
 
     // Check existence of GPS coordinates
     double latitude, longitude, altitude;
-    if ( exifReader->GPSLatitude( &latitude ) &&
-         exifReader->GPSLongitude( &longitude ) &&
-         exifReader->GPSAltitude( &altitude ) )
+    if ( exifReader.GPSLatitude( &latitude ) &&
+         exifReader.GPSLongitude( &longitude ) &&
+         exifReader.GPSAltitude( &altitude ) )
     {
       // Add XYZ position to the GPS position array
       switch (i_GPS_XYZ_method)
diff --git a/src/software/SfM/main_SfMInit_ImageListing.cpp b/src/software/SfM/main_SfMInit_ImageListing.cpp
index 8c37f9f4..482a97db 100644
--- a/src/software/SfM/main_SfMInit_ImageListing.cpp
+++ b/src/software/SfM/main_SfMInit_ImageListing.cpp
@@ -68,31 +68,28 @@ bool getGPS
   Vec3 & pose_center
 )
 {
-  std::unique_ptr<Exif_IO> exifReader(new Exif_IO_EasyExif);
-  if (exifReader)
+  Exif_IO_EasyExif exifReader;
+  // Try to parse EXIF metada & check existence of EXIF data
+  if ( exifReader.open( filename ) && exifReader.doesHaveExifInfo() )
   {
-    // Try to parse EXIF metada & check existence of EXIF data
-    if ( exifReader->open( filename ) && exifReader->doesHaveExifInfo() )
+    // Check existence of GPS coordinates
+    double latitude, longitude, altitude;
+    if ( exifReader.GPSLatitude( &latitude ) &&
+         exifReader.GPSLongitude( &longitude ) &&
+         exifReader.GPSAltitude( &altitude ) )
     {
-      // Check existence of GPS coordinates
-      double latitude, longitude, altitude;
-      if ( exifReader->GPSLatitude( &latitude ) &&
-           exifReader->GPSLongitude( &longitude ) &&
-           exifReader->GPSAltitude( &altitude ) )
+      // Add ECEF or UTM XYZ position to the GPS position array
+      switch (GPS_to_XYZ_method)
       {
-        // Add ECEF or UTM XYZ position to the GPS position array
-        switch (GPS_to_XYZ_method)
-        {
-          case 1:
-            pose_center = lla_to_utm( latitude, longitude, altitude );
-            break;
-          case 0:
-          default:
-            pose_center = lla_to_ecef( latitude, longitude, altitude );
-            break;
-        }
-        return true;
+        case 1:
+          pose_center = lla_to_utm( latitude, longitude, altitude );
+          break;
+        case 0:
+        default:
+          pose_center = lla_to_ecef( latitude, longitude, altitude );
+          break;
       }
+      return true;
     }
   }
   return false;
@@ -323,18 +320,18 @@ int main(int argc, char **argv)
     // If not manually provided or wrongly provided
     if (focal == -1)
     {
-      std::unique_ptr<Exif_IO> exifReader(new Exif_IO_EasyExif);
-      exifReader->open( sImageFilename );
+      Exif_IO_EasyExif exifReader;
+      exifReader.open( sImageFilename );
 
       const bool bHaveValidExifMetadata =
-        exifReader->doesHaveExifInfo()
-        && !exifReader->getModel().empty()
-        && !exifReader->getBrand().empty();
+        exifReader.doesHaveExifInfo()
+        && !exifReader.getModel().empty()
+        && !exifReader.getBrand().empty();
 
       if (bHaveValidExifMetadata) // If image contains meta data
       {
         // Handle case where focal length is equal to 0
-        if (exifReader->getFocal() == 0.0f)
+        if (exifReader.getFocal() == 0.0f)
         {
           error_report_stream
             << stlplus::basename_part(sImageFilename) << ": Focal length is missing." << "\n";
@@ -343,14 +340,14 @@ int main(int argc, char **argv)
         else
         // Create the image entry in the list file
         {
-          const std::string sCamModel = exifReader->getBrand() + " " + exifReader->getModel();
+          const std::string sCamModel = exifReader.getBrand() + " " + exifReader.getModel();
 
           Datasheet datasheet;
           if ( getInfo( sCamModel, vec_database, datasheet ))
           {
             // The camera model was found in the database so we can compute it's approximated focal length
             const double ccdw = datasheet.sensorSize_;
-            focal = std::max ( width, height ) * exifReader->getFocal() / ccdw;
+            focal = std::max ( width, height ) * exifReader.getFocal() / ccdw;
           }
           else
           {
-- 
2.43.0.windows.1

