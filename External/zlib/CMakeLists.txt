UpdateExternalLib("zlib" "https://github.com/madler/zlib.git" "09155eaa2f9270dc4ed1fa13e2b4b2613e6e4851")

set(SKIP_INSTALL_ALL ON CACHE BOOL "" FORCE)

set(BUILD_SHARED_LIBS ON)
set(ZLIB_BUILD_EXAMPLES OFF CACHE INTERNAL "" FORCE)

add_subdirectory(zlib EXCLUDE_FROM_ALL)

set(ZLIB_INCLUDE_DIR
	${CMAKE_CURRENT_SOURCE_DIR}/zlib
	${CMAKE_CURRENT_BINARY_DIR}/zlib
	CACHE INTERNAL "" FORCE
)
set(ZLIB_LIBRARY zlib CACHE INTERNAL "" FORCE)
set(ZLIB_LIBRARIES ${ZLIB_LIBRARY} CACHE INTERNAL "" FORCE)
set(ZLIB_FOUND TRUE CACHE INTERNAL "" FORCE)
add_library(ZLIB::ZLIB ALIAS zlib)

set(ZLIB_LIBRARY_DEBUG "zlib")
set(ZLIB_LIBRARY_RELEASE "zlib")

set_target_properties(zlib PROPERTIES FOLDER "External")
