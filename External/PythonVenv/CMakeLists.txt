execute_process(
    COMMAND Setup.bat "${Python3_EXECUTABLE}"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

set(deployed_files )

set(py_files
    ccimport/buildtools/writer.py
    ccimport/buildtools/__init__.py
    ccimport/buildmeta.py
    ccimport/compat.py
    ccimport/constants.py
    ccimport/core.py
    ccimport/global_cfg.py
    ccimport/loader.py
    ccimport/source_iter.py
    ccimport/utils.py
    ccimport/__init__.py
    cumm/conv/algospec/all.py
    cumm/conv/algospec/simt.py
    cumm/conv/algospec/turing.py
    cumm/conv/algospec/volta.py
    cumm/conv/algospec/__init__.py
    cumm/conv/bases.py
    cumm/conv/input_iters.py
    cumm/conv/kernel.py
    cumm/conv/main.py
    cumm/conv/nvrtc_code.py
    cumm/conv/params.py
    cumm/conv/sparse_iters.py
    cumm/conv/__init__.py
    cumm/cudasim/checkers.py
    cumm/cudasim/debug.py
    cumm/cudasim/__init__.py
    cumm/gemm/algospec/all.py
    cumm/gemm/algospec/bases.py
    cumm/gemm/algospec/core.py
    cumm/gemm/algospec/simt.py
    cumm/gemm/algospec/turing.py
    cumm/gemm/algospec/volta.py
    cumm/gemm/algospec/__init__.py
    cumm/gemm/arch/cpasync.py
    cumm/gemm/arch/instmma.py
    cumm/gemm/arch/ldmatrix.py
    cumm/gemm/arch/memory.py
    cumm/gemm/arch/tensorop.py
    cumm/gemm/arch/__init__.py
    cumm/gemm/blockmma/mma.py
    cumm/gemm/blockmma/mma_multistage.py
    cumm/gemm/blockmma/__init__.py
    cumm/gemm/core/metaarray.py
    cumm/gemm/core/__init__.py
    cumm/gemm/gemmmath/__init__.py
    cumm/gemm/outputs/__init__.py
    cumm/gemm/output_op/apply.py
    cumm/gemm/output_op/linear.py
    cumm/gemm/output_op/__init__.py
    cumm/gemm/wmma/simt.py
    cumm/gemm/wmma/turing.py
    cumm/gemm/wmma/volta.py
    cumm/gemm/wmma/__init__.py
    cumm/gemm/bases.py
    cumm/gemm/codeops.py
    cumm/gemm/constants.py
    cumm/gemm/kernel.py
    cumm/gemm/layout.py
    cumm/gemm/layout_tensorop.py
    cumm/gemm/main.py
    cumm/gemm/mask.py
    cumm/gemm/mask_iters.py
    cumm/gemm/nvrtc_code.py
    cumm/gemm/out_iters.py
    cumm/gemm/thread_map.py
    cumm/gemm/turing_iters.py
    cumm/gemm/turing_my_iters.py
    cumm/gemm/turing_out_iters.py
    cumm/gemm/utils.py
    cumm/gemm/volta_iters.py
    cumm/gemm/volta_out_iters.py
    cumm/gemm/__init__.py
    cumm/nvrtc/__init__.py
    cumm/tensorview/gemm.py
    cumm/tensorview/utils.py
    cumm/tensorview/__init__.py
    cumm/utils/richprint.py
    cumm/utils/__init__.py
    cumm/common.py
    cumm/constants.py
    cumm/dtypes.py
    cumm/__init__.py
    cumm/__version__.py
    google/protobuf/internal/api_implementation.py
    google/protobuf/internal/builder.py
    google/protobuf/internal/containers.py
    google/protobuf/internal/decoder.py
    google/protobuf/internal/encoder.py
    google/protobuf/internal/enum_type_wrapper.py
    google/protobuf/internal/extension_dict.py
    google/protobuf/internal/field_mask.py
    google/protobuf/internal/message_listener.py
    google/protobuf/internal/python_edition_defaults.py
    google/protobuf/internal/python_message.py
    google/protobuf/internal/type_checkers.py
    google/protobuf/internal/well_known_types.py
    google/protobuf/internal/wire_format.py
    google/protobuf/internal/__init__.py
    google/protobuf/pyext/__init__.py
    google/protobuf/descriptor.py
    google/protobuf/descriptor_database.py
    google/protobuf/descriptor_pb2.py
    google/protobuf/descriptor_pool.py
    google/protobuf/json_format.py
    google/protobuf/message.py
    google/protobuf/message_factory.py
    google/protobuf/reflection.py
    google/protobuf/runtime_version.py
    google/protobuf/symbol_database.py
    google/protobuf/text_encoding.py
    google/protobuf/text_format.py
    google/protobuf/unknown_fields.py
    google/protobuf/__init__.py
    lark/parsers/cyk.py
    lark/parsers/earley.py
    lark/parsers/earley_common.py
    lark/parsers/earley_forest.py
    lark/parsers/grammar_analysis.py
    lark/parsers/lalr_analysis.py
    lark/parsers/lalr_interactive_parser.py
    lark/parsers/lalr_parser.py
    lark/parsers/lalr_parser_state.py
    lark/parsers/xearley.py
    lark/parsers/__init__.py
    lark/common.py
    lark/exceptions.py
    lark/grammar.py
    lark/lark.py
    lark/lexer.py
    lark/load_grammar.py
    lark/parser_frontends.py
    lark/parse_tree_builder.py
    lark/tree.py
    lark/utils.py
    lark/visitors.py
    lark/__init__.py
    mpmath/calculus/approximation.py
    mpmath/calculus/calculus.py
    mpmath/calculus/differentiation.py
    mpmath/calculus/extrapolation.py
    mpmath/calculus/inverselaplace.py
    mpmath/calculus/odes.py
    mpmath/calculus/optimization.py
    mpmath/calculus/polynomials.py
    mpmath/calculus/quadrature.py
    mpmath/calculus/__init__.py
    mpmath/functions/bessel.py
    mpmath/functions/elliptic.py
    mpmath/functions/expintegrals.py
    mpmath/functions/factorials.py
    mpmath/functions/functions.py
    mpmath/functions/hypergeometric.py
    mpmath/functions/orthogonal.py
    mpmath/functions/qfunctions.py
    mpmath/functions/rszeta.py
    mpmath/functions/signals.py
    mpmath/functions/theta.py
    mpmath/functions/zeta.py
    mpmath/functions/zetazeros.py
    mpmath/functions/__init__.py
    mpmath/libmp/backend.py
    mpmath/libmp/gammazeta.py
    mpmath/libmp/libelefun.py
    mpmath/libmp/libhyper.py
    mpmath/libmp/libintmath.py
    mpmath/libmp/libmpc.py
    mpmath/libmp/libmpf.py
    mpmath/libmp/libmpi.py
    mpmath/libmp/__init__.py
    mpmath/matrices/calculus.py
    mpmath/matrices/eigen.py
    mpmath/matrices/eigen_symmetric.py
    mpmath/matrices/linalg.py
    mpmath/matrices/matrices.py
    mpmath/matrices/__init__.py
    mpmath/ctx_base.py
    mpmath/ctx_fp.py
    mpmath/ctx_iv.py
    mpmath/ctx_mp.py
    mpmath/ctx_mp_python.py
    mpmath/function_docs.py
    mpmath/identification.py
    mpmath/math2.py
    mpmath/rational.py
    mpmath/usertools.py
    mpmath/visualization.py
    mpmath/__init__.py
    ninja/ninja_syntax.py
    ninja/_version.py
    ninja/__init__.py
    numpy/fft/helper.py
    numpy/fft/_helper.py
    numpy/fft/_pocketfft.py
    numpy/fft/__init__.py
    numpy/lib/array_utils.py
    numpy/lib/format.py
    numpy/lib/introspect.py
    numpy/lib/mixins.py
    numpy/lib/npyio.py
    numpy/lib/scimath.py
    numpy/lib/stride_tricks.py
    numpy/lib/_arraypad_impl.py
    numpy/lib/_arraysetops_impl.py
    numpy/lib/_arrayterator_impl.py
    numpy/lib/_array_utils_impl.py
    numpy/lib/_datasource.py
    numpy/lib/_function_base_impl.py
    numpy/lib/_histograms_impl.py
    numpy/lib/_index_tricks_impl.py
    numpy/lib/_iotools.py
    numpy/lib/_nanfunctions_impl.py
    numpy/lib/_npyio_impl.py
    numpy/lib/_polynomial_impl.py
    numpy/lib/_scimath_impl.py
    numpy/lib/_shape_base_impl.py
    numpy/lib/_stride_tricks_impl.py
    numpy/lib/_twodim_base_impl.py
    numpy/lib/_type_check_impl.py
    numpy/lib/_ufunclike_impl.py
    numpy/lib/_utils_impl.py
    numpy/lib/_version.py
    numpy/lib/__init__.py
    numpy/linalg/linalg.py
    numpy/linalg/_linalg.py
    numpy/linalg/__init__.py
    numpy/matrixlib/defmatrix.py
    numpy/matrixlib/__init__.py
    numpy/random/_pickle.py
    numpy/random/__init__.py
    numpy/_core/arrayprint.py
    numpy/_core/einsumfunc.py
    numpy/_core/fromnumeric.py
    numpy/_core/function_base.py
    numpy/_core/getlimits.py
    numpy/_core/memmap.py
    numpy/_core/multiarray.py
    numpy/_core/numeric.py
    numpy/_core/numerictypes.py
    numpy/_core/overrides.py
    numpy/_core/printoptions.py
    numpy/_core/records.py
    numpy/_core/shape_base.py
    numpy/_core/umath.py
    numpy/_core/_add_newdocs.py
    numpy/_core/_add_newdocs_scalars.py
    numpy/_core/_asarray.py
    numpy/_core/_dtype.py
    numpy/_core/_dtype_ctypes.py
    numpy/_core/_exceptions.py
    numpy/_core/_internal.py
    numpy/_core/_machar.py
    numpy/_core/_methods.py
    numpy/_core/_string_helpers.py
    numpy/_core/_type_aliases.py
    numpy/_core/_ufunc_config.py
    numpy/_core/__init__.py
    numpy/_typing/_array_like.py
    numpy/_typing/_char_codes.py
    numpy/_typing/_dtype_like.py
    numpy/_typing/_nbit.py
    numpy/_typing/_nbit_base.py
    numpy/_typing/_nested_sequence.py
    numpy/_typing/_scalars.py
    numpy/_typing/_shape.py
    numpy/_typing/_ufunc.py
    numpy/_typing/__init__.py
    numpy/_utils/_convertions.py
    numpy/_utils/_inspect.py
    numpy/_utils/__init__.py
    numpy/dtypes.py
    numpy/exceptions.py
    numpy/version.py
    numpy/_array_api_info.py
    numpy/_distributor_init.py
    numpy/_expired_attrs_2_0.py
    numpy/_globals.py
    numpy/_pytesttester.py
    numpy/__config__.py
    numpy/__init__.py
    onnx/defs/__init__.py
    onnx/checker.py
    onnx/compose.py
    onnx/external_data_helper.py
    onnx/gen_proto.py
    onnx/helper.py
    onnx/hub.py
    onnx/mapping.py
    onnx/numpy_helper.py
    onnx/onnx_data_pb.py
    onnx/onnx_data_pb2.py
    onnx/onnx_ml_pb2.py
    onnx/onnx_operators_ml_pb2.py
    onnx/onnx_operators_pb.py
    onnx/onnx_pb.py
    onnx/parser.py
    onnx/printer.py
    onnx/serialization.py
    onnx/shape_inference.py
    onnx/subbyte.py
    onnx/utils.py
    onnx/version.py
    onnx/version_converter.py
    onnx/_custom_element_types.py
    onnx/__init__.py
    onnx2torch/node_converters/activations.py
    onnx2torch/node_converters/arg_extrema.py
    onnx2torch/node_converters/average_pool.py
    onnx2torch/node_converters/base_element_wise.py
    onnx2torch/node_converters/batch_norm.py
    onnx2torch/node_converters/binary_math_operations.py
    onnx2torch/node_converters/cast.py
    onnx2torch/node_converters/clip.py
    onnx2torch/node_converters/comparisons.py
    onnx2torch/node_converters/concat.py
    onnx2torch/node_converters/constant.py
    onnx2torch/node_converters/constant_of_shape.py
    onnx2torch/node_converters/conv.py
    onnx2torch/node_converters/cumsum.py
    onnx2torch/node_converters/depth_to_space.py
    onnx2torch/node_converters/dropout.py
    onnx2torch/node_converters/einsum.py
    onnx2torch/node_converters/expand.py
    onnx2torch/node_converters/eye_like.py
    onnx2torch/node_converters/flatten.py
    onnx2torch/node_converters/functions.py
    onnx2torch/node_converters/gather.py
    onnx2torch/node_converters/gemm.py
    onnx2torch/node_converters/global_average_pool.py
    onnx2torch/node_converters/identity.py
    onnx2torch/node_converters/instance_norm.py
    onnx2torch/node_converters/isinf.py
    onnx2torch/node_converters/isnan.py
    onnx2torch/node_converters/layer_norm.py
    onnx2torch/node_converters/logical.py
    onnx2torch/node_converters/lrn.py
    onnx2torch/node_converters/matmul.py
    onnx2torch/node_converters/max_pool.py
    onnx2torch/node_converters/mean.py
    onnx2torch/node_converters/min_max.py
    onnx2torch/node_converters/mod.py
    onnx2torch/node_converters/neg.py
    onnx2torch/node_converters/nms.py
    onnx2torch/node_converters/nonzero.py
    onnx2torch/node_converters/pad.py
    onnx2torch/node_converters/pow.py
    onnx2torch/node_converters/range.py
    onnx2torch/node_converters/reciprocal.py
    onnx2torch/node_converters/reduce.py
    onnx2torch/node_converters/registry.py
    onnx2torch/node_converters/reshape.py
    onnx2torch/node_converters/resize.py
    onnx2torch/node_converters/roialign.py
    onnx2torch/node_converters/roundings.py
    onnx2torch/node_converters/scatter_nd.py
    onnx2torch/node_converters/shape.py
    onnx2torch/node_converters/slice.py
    onnx2torch/node_converters/split.py
    onnx2torch/node_converters/squeeze.py
    onnx2torch/node_converters/sum.py
    onnx2torch/node_converters/tile.py
    onnx2torch/node_converters/topk.py
    onnx2torch/node_converters/transpose.py
    onnx2torch/node_converters/unsqueeze.py
    onnx2torch/node_converters/where.py
    onnx2torch/node_converters/__init__.py
    onnx2torch/utils/common.py
    onnx2torch/utils/custom_export_to_onnx.py
    onnx2torch/utils/dtype.py
    onnx2torch/utils/indices.py
    onnx2torch/utils/padding.py
    onnx2torch/utils/safe_shape_inference.py
    onnx2torch/utils/__init__.py
    onnx2torch/converter.py
    onnx2torch/onnx_graph.py
    onnx2torch/onnx_node.py
    onnx2torch/onnx_tensor.py
    onnx2torch/__init__.py
    pccm/builder/inliner.py
    pccm/builder/pybind.py
    pccm/builder/__init__.py
    pccm/core/buildmeta.py
    pccm/core/codegen.py
    pccm/core/funccode.py
    pccm/core/inspecttools.py
    pccm/core/markers.py
    pccm/core/parsers.py
    pccm/core/__init__.py
    pccm/middlewares/expose_main.py
    pccm/middlewares/pybind.py
    pccm/middlewares/__init__.py
    pccm/source/core.py
    pccm/source/__init__.py
    pccm/targets/cuda.py
    pccm/targets/cuda_ptx.py
    pccm/targets/__init__.py
    pccm/constants.py
    pccm/utils.py
    pccm/__init__.py
    portalocker/constants.py
    portalocker/exceptions.py
    portalocker/portalocker.py
    portalocker/redis.py
    portalocker/types.py
    portalocker/utils.py
    portalocker/__about__.py
    portalocker/__init__.py
    pybind11/commands.py
    pybind11/_version.py
    pybind11/__init__.py
    PIL/ExifTags.py
    PIL/features.py
    PIL/GimpGradientFile.py
    PIL/GimpPaletteFile.py
    PIL/Image.py
    PIL/ImageColor.py
    PIL/ImageDraw.py
    PIL/ImageEnhance.py
    PIL/ImageFilter.py
    PIL/ImageFont.py
    PIL/ImageMode.py
    PIL/ImageOps.py
    PIL/ImagePalette.py
    PIL/ImageStat.py
    PIL/PaletteFile.py
    PIL/TiffTags.py
    PIL/_binary.py
    PIL/_deprecate.py
    PIL/_typing.py
    PIL/_util.py
    PIL/_version.py
    PIL/__init__.py
    safetensors/torch.py
    safetensors/__init__.py
    spconv/pytorch/constants.py
    spconv/pytorch/conv.py
    spconv/pytorch/core.py
    spconv/pytorch/cppcore.py
    spconv/pytorch/functional.py
    spconv/pytorch/hash.py
    spconv/pytorch/identity.py
    spconv/pytorch/modules.py
    spconv/pytorch/ops.py
    spconv/pytorch/pool.py
    spconv/pytorch/tables.py
    spconv/pytorch/__init__.py
    spconv/utils/__init__.py
    spconv/algo.py
    spconv/algocore.py
    spconv/build.py
    spconv/constants.py
    spconv/core.py
    spconv/cppconstants.py
    spconv/debug_utils.py
    spconv/tools.py
    spconv/__init__.py
    spconv/__version__.py
    sympy/algebras/quaternion.py
    sympy/algebras/__init__.py
    sympy/assumptions/relation/binrel.py
    sympy/assumptions/relation/equality.py
    sympy/assumptions/relation/__init__.py
    sympy/assumptions/ask.py
    sympy/assumptions/ask_generated.py
    sympy/assumptions/assume.py
    sympy/assumptions/cnf.py
    sympy/assumptions/refine.py
    sympy/assumptions/__init__.py
    sympy/calculus/accumulationbounds.py
    sympy/calculus/euler.py
    sympy/calculus/finite_diff.py
    sympy/calculus/singularities.py
    sympy/calculus/util.py
    sympy/calculus/__init__.py
    sympy/concrete/expr_with_intlimits.py
    sympy/concrete/expr_with_limits.py
    sympy/concrete/gosper.py
    sympy/concrete/products.py
    sympy/concrete/summations.py
    sympy/concrete/__init__.py
    sympy/core/add.py
    sympy/core/alphabets.py
    sympy/core/assumptions.py
    sympy/core/assumptions_generated.py
    sympy/core/basic.py
    sympy/core/cache.py
    sympy/core/containers.py
    sympy/core/core.py
    sympy/core/coreerrors.py
    sympy/core/decorators.py
    sympy/core/evalf.py
    sympy/core/expr.py
    sympy/core/exprtools.py
    sympy/core/facts.py
    sympy/core/function.py
    sympy/core/intfunc.py
    sympy/core/kind.py
    sympy/core/logic.py
    sympy/core/mod.py
    sympy/core/mul.py
    sympy/core/multidimensional.py
    sympy/core/numbers.py
    sympy/core/operations.py
    sympy/core/parameters.py
    sympy/core/power.py
    sympy/core/random.py
    sympy/core/relational.py
    sympy/core/rules.py
    sympy/core/singleton.py
    sympy/core/sorting.py
    sympy/core/symbol.py
    sympy/core/sympify.py
    sympy/core/traversal.py
    sympy/core/_print_helpers.py
    sympy/core/__init__.py
    sympy/discrete/convolutions.py
    sympy/discrete/transforms.py
    sympy/discrete/__init__.py
    sympy/external/gmpy.py
    sympy/external/importtools.py
    sympy/external/ntheory.py
    sympy/external/pythonmpq.py
    sympy/external/__init__.py
    sympy/functions/combinatorial/factorials.py
    sympy/functions/combinatorial/numbers.py
    sympy/functions/combinatorial/__init__.py
    sympy/functions/elementary/complexes.py
    sympy/functions/elementary/exponential.py
    sympy/functions/elementary/hyperbolic.py
    sympy/functions/elementary/integers.py
    sympy/functions/elementary/miscellaneous.py
    sympy/functions/elementary/piecewise.py
    sympy/functions/elementary/trigonometric.py
    sympy/functions/elementary/_trigonometric_special.py
    sympy/functions/elementary/__init__.py
    sympy/functions/special/bessel.py
    sympy/functions/special/beta_functions.py
    sympy/functions/special/bsplines.py
    sympy/functions/special/delta_functions.py
    sympy/functions/special/elliptic_integrals.py
    sympy/functions/special/error_functions.py
    sympy/functions/special/gamma_functions.py
    sympy/functions/special/hyper.py
    sympy/functions/special/mathieu_functions.py
    sympy/functions/special/polynomials.py
    sympy/functions/special/singularity_functions.py
    sympy/functions/special/spherical_harmonics.py
    sympy/functions/special/tensor_functions.py
    sympy/functions/special/zeta_functions.py
    sympy/functions/special/__init__.py
    sympy/functions/__init__.py
    sympy/geometry/curve.py
    sympy/geometry/ellipse.py
    sympy/geometry/entity.py
    sympy/geometry/exceptions.py
    sympy/geometry/line.py
    sympy/geometry/parabola.py
    sympy/geometry/plane.py
    sympy/geometry/point.py
    sympy/geometry/polygon.py
    sympy/geometry/util.py
    sympy/geometry/__init__.py
    sympy/integrals/deltafunctions.py
    sympy/integrals/integrals.py
    sympy/integrals/laplace.py
    sympy/integrals/meijerint.py
    sympy/integrals/rationaltools.py
    sympy/integrals/singularityfunctions.py
    sympy/integrals/transforms.py
    sympy/integrals/trigonometry.py
    sympy/integrals/__init__.py
    sympy/interactive/printing.py
    sympy/interactive/session.py
    sympy/interactive/traversal.py
    sympy/interactive/__init__.py
    sympy/logic/boolalg.py
    sympy/logic/inference.py
    sympy/logic/__init__.py
    sympy/matrices/expressions/adjoint.py
    sympy/matrices/expressions/blockmatrix.py
    sympy/matrices/expressions/companion.py
    sympy/matrices/expressions/determinant.py
    sympy/matrices/expressions/diagonal.py
    sympy/matrices/expressions/dotproduct.py
    sympy/matrices/expressions/funcmatrix.py
    sympy/matrices/expressions/hadamard.py
    sympy/matrices/expressions/inverse.py
    sympy/matrices/expressions/kronecker.py
    sympy/matrices/expressions/matadd.py
    sympy/matrices/expressions/matexpr.py
    sympy/matrices/expressions/matmul.py
    sympy/matrices/expressions/matpow.py
    sympy/matrices/expressions/permutation.py
    sympy/matrices/expressions/sets.py
    sympy/matrices/expressions/slice.py
    sympy/matrices/expressions/special.py
    sympy/matrices/expressions/trace.py
    sympy/matrices/expressions/transpose.py
    sympy/matrices/expressions/_shape.py
    sympy/matrices/expressions/__init__.py
    sympy/matrices/decompositions.py
    sympy/matrices/dense.py
    sympy/matrices/determinant.py
    sympy/matrices/eigen.py
    sympy/matrices/exceptions.py
    sympy/matrices/graph.py
    sympy/matrices/immutable.py
    sympy/matrices/inverse.py
    sympy/matrices/kind.py
    sympy/matrices/matrixbase.py
    sympy/matrices/reductions.py
    sympy/matrices/repmatrix.py
    sympy/matrices/solvers.py
    sympy/matrices/sparse.py
    sympy/matrices/sparsetools.py
    sympy/matrices/subspaces.py
    sympy/matrices/utilities.py
    sympy/matrices/__init__.py
    sympy/multipledispatch/conflict.py
    sympy/multipledispatch/core.py
    sympy/multipledispatch/dispatcher.py
    sympy/multipledispatch/utils.py
    sympy/multipledispatch/__init__.py
    sympy/ntheory/continued_fraction.py
    sympy/ntheory/digits.py
    sympy/ntheory/ecm.py
    sympy/ntheory/egyptian_fraction.py
    sympy/ntheory/factor_.py
    sympy/ntheory/generate.py
    sympy/ntheory/modular.py
    sympy/ntheory/multinomial.py
    sympy/ntheory/partitions_.py
    sympy/ntheory/primetest.py
    sympy/ntheory/qs.py
    sympy/ntheory/residue_ntheory.py
    sympy/ntheory/__init__.py
    sympy/parsing/sympy_parser.py
    sympy/parsing/__init__.py
    sympy/plotting/backends/matplotlibbackend/matplotlib.py
    sympy/plotting/backends/matplotlibbackend/__init__.py
    sympy/plotting/backends/textbackend/text.py
    sympy/plotting/backends/textbackend/__init__.py
    sympy/plotting/backends/base_backend.py
    sympy/plotting/backends/__init__.py
    sympy/plotting/intervalmath/interval_arithmetic.py
    sympy/plotting/intervalmath/interval_membership.py
    sympy/plotting/intervalmath/lib_interval.py
    sympy/plotting/intervalmath/__init__.py
    sympy/plotting/pygletplot/__init__.py
    sympy/plotting/plot.py
    sympy/plotting/plotgrid.py
    sympy/plotting/plot_implicit.py
    sympy/plotting/series.py
    sympy/plotting/textplot.py
    sympy/plotting/utils.py
    sympy/plotting/__init__.py
    sympy/polys/agca/extensions.py
    sympy/polys/agca/homomorphisms.py
    sympy/polys/agca/ideals.py
    sympy/polys/agca/modules.py
    sympy/polys/agca/__init__.py
    sympy/polys/domains/algebraicfield.py
    sympy/polys/domains/characteristiczero.py
    sympy/polys/domains/complexfield.py
    sympy/polys/domains/compositedomain.py
    sympy/polys/domains/domain.py
    sympy/polys/domains/domainelement.py
    sympy/polys/domains/expressiondomain.py
    sympy/polys/domains/expressionrawdomain.py
    sympy/polys/domains/field.py
    sympy/polys/domains/finitefield.py
    sympy/polys/domains/fractionfield.py
    sympy/polys/domains/gaussiandomains.py
    sympy/polys/domains/gmpyfinitefield.py
    sympy/polys/domains/gmpyintegerring.py
    sympy/polys/domains/gmpyrationalfield.py
    sympy/polys/domains/groundtypes.py
    sympy/polys/domains/integerring.py
    sympy/polys/domains/modularinteger.py
    sympy/polys/domains/mpelements.py
    sympy/polys/domains/polynomialring.py
    sympy/polys/domains/pythonfinitefield.py
    sympy/polys/domains/pythonintegerring.py
    sympy/polys/domains/pythonrational.py
    sympy/polys/domains/pythonrationalfield.py
    sympy/polys/domains/rationalfield.py
    sympy/polys/domains/realfield.py
    sympy/polys/domains/ring.py
    sympy/polys/domains/simpledomain.py
    sympy/polys/domains/__init__.py
    sympy/polys/matrices/ddm.py
    sympy/polys/matrices/dense.py
    sympy/polys/matrices/dfm.py
    sympy/polys/matrices/domainmatrix.py
    sympy/polys/matrices/domainscalar.py
    sympy/polys/matrices/eigen.py
    sympy/polys/matrices/exceptions.py
    sympy/polys/matrices/linsolve.py
    sympy/polys/matrices/lll.py
    sympy/polys/matrices/normalforms.py
    sympy/polys/matrices/rref.py
    sympy/polys/matrices/sdm.py
    sympy/polys/matrices/_typing.py
    sympy/polys/matrices/__init__.py
    sympy/polys/numberfields/basis.py
    sympy/polys/numberfields/exceptions.py
    sympy/polys/numberfields/galoisgroups.py
    sympy/polys/numberfields/galois_resolvents.py
    sympy/polys/numberfields/minpoly.py
    sympy/polys/numberfields/modules.py
    sympy/polys/numberfields/primes.py
    sympy/polys/numberfields/subfield.py
    sympy/polys/numberfields/utilities.py
    sympy/polys/numberfields/__init__.py
    sympy/polys/appellseqs.py
    sympy/polys/compatibility.py
    sympy/polys/constructor.py
    sympy/polys/densearith.py
    sympy/polys/densebasic.py
    sympy/polys/densetools.py
    sympy/polys/domainmatrix.py
    sympy/polys/euclidtools.py
    sympy/polys/factortools.py
    sympy/polys/fglmtools.py
    sympy/polys/fields.py
    sympy/polys/galoistools.py
    sympy/polys/groebnertools.py
    sympy/polys/heuristicgcd.py
    sympy/polys/monomials.py
    sympy/polys/orderings.py
    sympy/polys/orthopolys.py
    sympy/polys/partfrac.py
    sympy/polys/polyclasses.py
    sympy/polys/polyconfig.py
    sympy/polys/polyerrors.py
    sympy/polys/polyfuncs.py
    sympy/polys/polyoptions.py
    sympy/polys/polyquinticconst.py
    sympy/polys/polyroots.py
    sympy/polys/polytools.py
    sympy/polys/polyutils.py
    sympy/polys/rationaltools.py
    sympy/polys/rings.py
    sympy/polys/ring_series.py
    sympy/polys/rootisolation.py
    sympy/polys/rootoftools.py
    sympy/polys/solvers.py
    sympy/polys/specialpolys.py
    sympy/polys/sqfreetools.py
    sympy/polys/__init__.py
    sympy/printing/pretty/pretty.py
    sympy/printing/pretty/pretty_symbology.py
    sympy/printing/pretty/stringpict.py
    sympy/printing/pretty/__init__.py
    sympy/printing/codeprinter.py
    sympy/printing/conventions.py
    sympy/printing/defaults.py
    sympy/printing/dot.py
    sympy/printing/glsl.py
    sympy/printing/gtk.py
    sympy/printing/jscode.py
    sympy/printing/julia.py
    sympy/printing/lambdarepr.py
    sympy/printing/latex.py
    sympy/printing/maple.py
    sympy/printing/mathematica.py
    sympy/printing/mathml.py
    sympy/printing/numpy.py
    sympy/printing/octave.py
    sympy/printing/precedence.py
    sympy/printing/preview.py
    sympy/printing/printer.py
    sympy/printing/pycode.py
    sympy/printing/python.py
    sympy/printing/rcode.py
    sympy/printing/repr.py
    sympy/printing/rust.py
    sympy/printing/smtlib.py
    sympy/printing/str.py
    sympy/printing/tableform.py
    sympy/printing/tree.py
    sympy/printing/__init__.py
    sympy/series/approximants.py
    sympy/series/formal.py
    sympy/series/fourier.py
    sympy/series/gruntz.py
    sympy/series/limits.py
    sympy/series/limitseq.py
    sympy/series/order.py
    sympy/series/residues.py
    sympy/series/sequences.py
    sympy/series/series.py
    sympy/series/series_class.py
    sympy/series/__init__.py
    sympy/sets/handlers/comparison.py
    sympy/sets/handlers/intersection.py
    sympy/sets/handlers/union.py
    sympy/sets/handlers/__init__.py
    sympy/sets/conditionset.py
    sympy/sets/contains.py
    sympy/sets/fancysets.py
    sympy/sets/ordinals.py
    sympy/sets/powerset.py
    sympy/sets/sets.py
    sympy/sets/__init__.py
    sympy/simplify/combsimp.py
    sympy/simplify/cse_main.py
    sympy/simplify/cse_opts.py
    sympy/simplify/epathtools.py
    sympy/simplify/fu.py
    sympy/simplify/gammasimp.py
    sympy/simplify/hyperexpand.py
    sympy/simplify/powsimp.py
    sympy/simplify/radsimp.py
    sympy/simplify/ratsimp.py
    sympy/simplify/simplify.py
    sympy/simplify/sqrtdenest.py
    sympy/simplify/trigsimp.py
    sympy/simplify/__init__.py
    sympy/solvers/diophantine/diophantine.py
    sympy/solvers/diophantine/__init__.py
    sympy/solvers/ode/hypergeometric.py
    sympy/solvers/ode/lie_group.py
    sympy/solvers/ode/nonhomogeneous.py
    sympy/solvers/ode/ode.py
    sympy/solvers/ode/riccati.py
    sympy/solvers/ode/single.py
    sympy/solvers/ode/subscheck.py
    sympy/solvers/ode/systems.py
    sympy/solvers/ode/__init__.py
    sympy/solvers/bivariate.py
    sympy/solvers/decompogen.py
    sympy/solvers/deutils.py
    sympy/solvers/inequalities.py
    sympy/solvers/pde.py
    sympy/solvers/polysys.py
    sympy/solvers/recurr.py
    sympy/solvers/simplex.py
    sympy/solvers/solvers.py
    sympy/solvers/solveset.py
    sympy/solvers/__init__.py
    sympy/strategies/branch/core.py
    sympy/strategies/branch/tools.py
    sympy/strategies/branch/traverse.py
    sympy/strategies/branch/__init__.py
    sympy/strategies/core.py
    sympy/strategies/rl.py
    sympy/strategies/tools.py
    sympy/strategies/traverse.py
    sympy/strategies/tree.py
    sympy/strategies/util.py
    sympy/strategies/__init__.py
    sympy/tensor/array/arrayop.py
    sympy/tensor/array/array_comprehension.py
    sympy/tensor/array/dense_ndim_array.py
    sympy/tensor/array/mutable_ndim_array.py
    sympy/tensor/array/ndim_array.py
    sympy/tensor/array/sparse_ndim_array.py
    sympy/tensor/array/__init__.py
    sympy/tensor/functions.py
    sympy/tensor/indexed.py
    sympy/tensor/index_methods.py
    sympy/tensor/__init__.py
    sympy/utilities/mathml/__init__.py
    sympy/utilities/decorator.py
    sympy/utilities/enumerative.py
    sympy/utilities/exceptions.py
    sympy/utilities/iterables.py
    sympy/utilities/lambdify.py
    sympy/utilities/magic.py
    sympy/utilities/memoization.py
    sympy/utilities/misc.py
    sympy/utilities/source.py
    sympy/utilities/timeutils.py
    sympy/utilities/__init__.py
    sympy/release.py
    sympy/__init__.py
    torch/accelerator/_utils.py
    torch/accelerator/__init__.py
    torch/amp/autocast_mode.py
    torch/amp/grad_scaler.py
    torch/amp/__init__.py
    torch/ao/nn/intrinsic/modules/fused.py
    torch/ao/nn/intrinsic/modules/__init__.py
    torch/ao/nn/intrinsic/qat/modules/conv_fused.py
    torch/ao/nn/intrinsic/qat/modules/linear_fused.py
    torch/ao/nn/intrinsic/qat/modules/linear_relu.py
    torch/ao/nn/intrinsic/qat/modules/__init__.py
    torch/ao/nn/intrinsic/qat/__init__.py
    torch/ao/nn/intrinsic/quantized/dynamic/modules/linear_relu.py
    torch/ao/nn/intrinsic/quantized/dynamic/modules/__init__.py
    torch/ao/nn/intrinsic/quantized/dynamic/__init__.py
    torch/ao/nn/intrinsic/quantized/modules/bn_relu.py
    torch/ao/nn/intrinsic/quantized/modules/conv_add.py
    torch/ao/nn/intrinsic/quantized/modules/conv_relu.py
    torch/ao/nn/intrinsic/quantized/modules/linear_relu.py
    torch/ao/nn/intrinsic/quantized/modules/__init__.py
    torch/ao/nn/intrinsic/quantized/__init__.py
    torch/ao/nn/intrinsic/__init__.py
    torch/ao/nn/qat/dynamic/modules/linear.py
    torch/ao/nn/qat/dynamic/modules/__init__.py
    torch/ao/nn/qat/dynamic/__init__.py
    torch/ao/nn/qat/modules/conv.py
    torch/ao/nn/qat/modules/embedding_ops.py
    torch/ao/nn/qat/modules/linear.py
    torch/ao/nn/qat/modules/__init__.py
    torch/ao/nn/qat/__init__.py
    torch/ao/nn/quantizable/modules/activation.py
    torch/ao/nn/quantizable/modules/rnn.py
    torch/ao/nn/quantizable/modules/__init__.py
    torch/ao/nn/quantizable/__init__.py
    torch/ao/nn/quantized/dynamic/modules/conv.py
    torch/ao/nn/quantized/dynamic/modules/linear.py
    torch/ao/nn/quantized/dynamic/modules/rnn.py
    torch/ao/nn/quantized/dynamic/modules/__init__.py
    torch/ao/nn/quantized/dynamic/__init__.py
    torch/ao/nn/quantized/modules/activation.py
    torch/ao/nn/quantized/modules/batchnorm.py
    torch/ao/nn/quantized/modules/conv.py
    torch/ao/nn/quantized/modules/dropout.py
    torch/ao/nn/quantized/modules/embedding_ops.py
    torch/ao/nn/quantized/modules/functional_modules.py
    torch/ao/nn/quantized/modules/linear.py
    torch/ao/nn/quantized/modules/normalization.py
    torch/ao/nn/quantized/modules/rnn.py
    torch/ao/nn/quantized/modules/utils.py
    torch/ao/nn/quantized/modules/__init__.py
    torch/ao/nn/quantized/reference/modules/conv.py
    torch/ao/nn/quantized/reference/modules/linear.py
    torch/ao/nn/quantized/reference/modules/rnn.py
    torch/ao/nn/quantized/reference/modules/sparse.py
    torch/ao/nn/quantized/reference/modules/utils.py
    torch/ao/nn/quantized/reference/modules/__init__.py
    torch/ao/nn/quantized/reference/__init__.py
    torch/ao/nn/quantized/functional.py
    torch/ao/nn/quantized/__init__.py
    torch/ao/nn/sparse/quantized/dynamic/linear.py
    torch/ao/nn/sparse/quantized/dynamic/__init__.py
    torch/ao/nn/sparse/quantized/linear.py
    torch/ao/nn/sparse/quantized/utils.py
    torch/ao/nn/sparse/quantized/__init__.py
    torch/ao/nn/sparse/__init__.py
    torch/ao/nn/__init__.py
    torch/ao/ns/fx/ns_types.py
    torch/ao/ns/fx/utils.py
    torch/ao/ns/fx/__init__.py
    torch/ao/ns/__init__.py
    torch/ao/quantization/pt2e/export_utils.py
    torch/ao/quantization/pt2e/graph_utils.py
    torch/ao/quantization/pt2e/_numeric_debugger.py
    torch/ao/quantization/pt2e/__init__.py
    torch/ao/quantization/fake_quantize.py
    torch/ao/quantization/fuser_method_mappings.py
    torch/ao/quantization/fuse_modules.py
    torch/ao/quantization/observer.py
    torch/ao/quantization/qconfig.py
    torch/ao/quantization/qconfig_mapping.py
    torch/ao/quantization/quantization_mappings.py
    torch/ao/quantization/quantize.py
    torch/ao/quantization/quantize_jit.py
    torch/ao/quantization/quant_type.py
    torch/ao/quantization/stubs.py
    torch/ao/quantization/utils.py
    torch/ao/quantization/__init__.py
    torch/ao/__init__.py
    torch/autograd/anomaly_mode.py
    torch/autograd/forward_ad.py
    torch/autograd/function.py
    torch/autograd/functional.py
    torch/autograd/gradcheck.py
    torch/autograd/grad_mode.py
    torch/autograd/graph.py
    torch/autograd/profiler.py
    torch/autograd/profiler_util.py
    torch/autograd/variable.py
    torch/autograd/__init__.py
    torch/backends/cpu/__init__.py
    torch/backends/cuda/__init__.py
    torch/backends/cudnn/__init__.py
    torch/backends/cusparselt/__init__.py
    torch/backends/mha/__init__.py
    torch/backends/mkl/__init__.py
    torch/backends/mkldnn/__init__.py
    torch/backends/mps/__init__.py
    torch/backends/nnpack/__init__.py
    torch/backends/openmp/__init__.py
    torch/backends/quantized/__init__.py
    torch/backends/__init__.py
    torch/compiler/config.py
    torch/compiler/__init__.py
    torch/cpu/amp/autocast_mode.py
    torch/cpu/amp/grad_scaler.py
    torch/cpu/amp/__init__.py
    torch/cpu/__init__.py
    torch/cuda/amp/autocast_mode.py
    torch/cuda/amp/common.py
    torch/cuda/amp/grad_scaler.py
    torch/cuda/amp/__init__.py
    torch/cuda/gds.py
    torch/cuda/graphs.py
    torch/cuda/jiterator.py
    torch/cuda/memory.py
    torch/cuda/nccl.py
    torch/cuda/nvtx.py
    torch/cuda/profiler.py
    torch/cuda/random.py
    torch/cuda/sparse.py
    torch/cuda/streams.py
    torch/cuda/tunable.py
    torch/cuda/_memory_viz.py
    torch/cuda/_utils.py
    torch/cuda/__init__.py
    torch/distributed/algorithms/_checkpoint/checkpoint_wrapper.py
    torch/distributed/algorithms/_checkpoint/__init__.py
    torch/distributed/algorithms/_comm_hooks/default_hooks.py
    torch/distributed/algorithms/_comm_hooks/__init__.py
    torch/distributed/algorithms/join.py
    torch/distributed/algorithms/__init__.py
    torch/distributed/autograd/__init__.py
    torch/distributed/fsdp/_fully_shard/_fsdp_api.py
    torch/distributed/fsdp/_fully_shard/_fsdp_collectives.py
    torch/distributed/fsdp/_fully_shard/_fsdp_common.py
    torch/distributed/fsdp/_fully_shard/_fsdp_init.py
    torch/distributed/fsdp/_fully_shard/_fsdp_param.py
    torch/distributed/fsdp/_fully_shard/_fsdp_param_group.py
    torch/distributed/fsdp/_fully_shard/_fsdp_state.py
    torch/distributed/fsdp/_fully_shard/_fully_shard.py
    torch/distributed/fsdp/_fully_shard/__init__.py
    torch/distributed/fsdp/api.py
    torch/distributed/fsdp/fully_sharded_data_parallel.py
    torch/distributed/fsdp/wrap.py
    torch/distributed/fsdp/_common_utils.py
    torch/distributed/fsdp/_debug_utils.py
    torch/distributed/fsdp/_dynamo_utils.py
    torch/distributed/fsdp/_exec_order_utils.py
    torch/distributed/fsdp/_flat_param.py
    torch/distributed/fsdp/_fsdp_extensions.py
    torch/distributed/fsdp/_init_utils.py
    torch/distributed/fsdp/_limiter_utils.py
    torch/distributed/fsdp/_optim_utils.py
    torch/distributed/fsdp/_runtime_utils.py
    torch/distributed/fsdp/_shard_utils.py
    torch/distributed/fsdp/_state_dict_utils.py
    torch/distributed/fsdp/_traversal_utils.py
    torch/distributed/fsdp/_unshard_param_utils.py
    torch/distributed/fsdp/_wrap_utils.py
    torch/distributed/fsdp/__init__.py
    torch/distributed/nn/functional.py
    torch/distributed/nn/__init__.py
    torch/distributed/rpc/__init__.py
    torch/distributed/tensor/parallel/api.py
    torch/distributed/tensor/parallel/fsdp.py
    torch/distributed/tensor/parallel/loss.py
    torch/distributed/tensor/parallel/style.py
    torch/distributed/tensor/parallel/_data_parallel_utils.py
    torch/distributed/tensor/parallel/_utils.py
    torch/distributed/tensor/parallel/__init__.py
    torch/distributed/tensor/_ops/utils.py
    torch/distributed/tensor/_ops/_common_rules.py
    torch/distributed/tensor/_ops/_conv_ops.py
    torch/distributed/tensor/_ops/_einsum_strategy.py
    torch/distributed/tensor/_ops/_embedding_ops.py
    torch/distributed/tensor/_ops/_experimental_ops.py
    torch/distributed/tensor/_ops/_math_ops.py
    torch/distributed/tensor/_ops/_matrix_ops.py
    torch/distributed/tensor/_ops/_pointwise_ops.py
    torch/distributed/tensor/_ops/_random_ops.py
    torch/distributed/tensor/_ops/_tensor_ops.py
    torch/distributed/tensor/_ops/_view_ops.py
    torch/distributed/tensor/_ops/__init__.py
    torch/distributed/tensor/device_mesh.py
    torch/distributed/tensor/placement_types.py
    torch/distributed/tensor/_api.py
    torch/distributed/tensor/_collective_utils.py
    torch/distributed/tensor/_dispatch.py
    torch/distributed/tensor/_dtensor_spec.py
    torch/distributed/tensor/_op_schema.py
    torch/distributed/tensor/_random.py
    torch/distributed/tensor/_redistribute.py
    torch/distributed/tensor/_sharding_prop.py
    torch/distributed/tensor/_tp_conv.py
    torch/distributed/tensor/_utils.py
    torch/distributed/tensor/__init__.py
    torch/distributed/_composable/checkpoint_activation.py
    torch/distributed/_composable/contract.py
    torch/distributed/_composable/replicate.py
    torch/distributed/_composable/__init__.py
    torch/distributed/_shard/sharded_tensor/_ops/binary_cmp.py
    torch/distributed/_shard/sharded_tensor/_ops/init.py
    torch/distributed/_shard/sharded_tensor/_ops/misc_ops.py
    torch/distributed/_shard/sharded_tensor/_ops/tensor_ops.py
    torch/distributed/_shard/sharded_tensor/_ops/_common.py
    torch/distributed/_shard/sharded_tensor/_ops/__init__.py
    torch/distributed/_shard/sharded_tensor/api.py
    torch/distributed/_shard/sharded_tensor/metadata.py
    torch/distributed/_shard/sharded_tensor/reshard.py
    torch/distributed/_shard/sharded_tensor/shard.py
    torch/distributed/_shard/sharded_tensor/utils.py
    torch/distributed/_shard/sharded_tensor/__init__.py
    torch/distributed/_shard/sharding_plan/api.py
    torch/distributed/_shard/sharding_plan/__init__.py
    torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/embedding.py
    torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/embedding_bag.py
    torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/_common.py
    torch/distributed/_shard/sharding_spec/chunk_sharding_spec_ops/__init__.py
    torch/distributed/_shard/sharding_spec/api.py
    torch/distributed/_shard/sharding_spec/chunk_sharding_spec.py
    torch/distributed/_shard/sharding_spec/_internals.py
    torch/distributed/_shard/sharding_spec/__init__.py
    torch/distributed/_shard/api.py
    torch/distributed/_shard/common_op_utils.py
    torch/distributed/_shard/metadata.py
    torch/distributed/_shard/op_registry_utils.py
    torch/distributed/_shard/sharder.py
    torch/distributed/_shard/_utils.py
    torch/distributed/_shard/__init__.py
    torch/distributed/c10d_logger.py
    torch/distributed/constants.py
    torch/distributed/device_mesh.py
    torch/distributed/distributed_c10d.py
    torch/distributed/logging_handlers.py
    torch/distributed/remote_device.py
    torch/distributed/rendezvous.py
    torch/distributed/utils.py
    torch/distributed/_composable_state.py
    torch/distributed/_functional_collectives.py
    torch/distributed/_functional_collectives_impl.py
    torch/distributed/_state_dict_utils.py
    torch/distributed/__init__.py
    torch/distributions/bernoulli.py
    torch/distributions/beta.py
    torch/distributions/binomial.py
    torch/distributions/categorical.py
    torch/distributions/cauchy.py
    torch/distributions/chi2.py
    torch/distributions/constraints.py
    torch/distributions/constraint_registry.py
    torch/distributions/continuous_bernoulli.py
    torch/distributions/dirichlet.py
    torch/distributions/distribution.py
    torch/distributions/exponential.py
    torch/distributions/exp_family.py
    torch/distributions/fishersnedecor.py
    torch/distributions/gamma.py
    torch/distributions/geometric.py
    torch/distributions/gumbel.py
    torch/distributions/half_cauchy.py
    torch/distributions/half_normal.py
    torch/distributions/independent.py
    torch/distributions/inverse_gamma.py
    torch/distributions/kl.py
    torch/distributions/kumaraswamy.py
    torch/distributions/laplace.py
    torch/distributions/lkj_cholesky.py
    torch/distributions/logistic_normal.py
    torch/distributions/log_normal.py
    torch/distributions/lowrank_multivariate_normal.py
    torch/distributions/mixture_same_family.py
    torch/distributions/multinomial.py
    torch/distributions/multivariate_normal.py
    torch/distributions/negative_binomial.py
    torch/distributions/normal.py
    torch/distributions/one_hot_categorical.py
    torch/distributions/pareto.py
    torch/distributions/poisson.py
    torch/distributions/relaxed_bernoulli.py
    torch/distributions/relaxed_categorical.py
    torch/distributions/studentT.py
    torch/distributions/transformed_distribution.py
    torch/distributions/transforms.py
    torch/distributions/uniform.py
    torch/distributions/utils.py
    torch/distributions/von_mises.py
    torch/distributions/weibull.py
    torch/distributions/wishart.py
    torch/distributions/__init__.py
    torch/export/decomp_utils.py
    torch/export/dynamic_shapes.py
    torch/export/exported_program.py
    torch/export/graph_signature.py
    torch/export/unflatten.py
    torch/export/_remove_effect_tokens_pass.py
    torch/export/_tree_utils.py
    torch/export/__init__.py
    torch/fft/__init__.py
    torch/func/__init__.py
    torch/futures/__init__.py
    torch/fx/experimental/const_fold.py
    torch/fx/experimental/proxy_tensor.py
    torch/fx/experimental/recording.py
    torch/fx/experimental/symbolic_shapes.py
    torch/fx/experimental/sym_node.py
    torch/fx/experimental/_backward_state.py
    torch/fx/experimental/_config.py
    torch/fx/experimental/_constant_symnode.py
    torch/fx/experimental/__init__.py
    torch/fx/passes/infra/pass_base.py
    torch/fx/passes/infra/pass_manager.py
    torch/fx/passes/infra/__init__.py
    torch/fx/passes/utils/common.py
    torch/fx/passes/utils/matcher_utils.py
    torch/fx/passes/utils/source_matcher_utils.py
    torch/fx/passes/utils/__init__.py
    torch/fx/passes/fake_tensor_prop.py
    torch/fx/passes/graph_drawer.py
    torch/fx/passes/graph_manipulation.py
    torch/fx/passes/graph_transform_observer.py
    torch/fx/passes/net_min_base.py
    torch/fx/passes/operator_support.py
    torch/fx/passes/param_fetch.py
    torch/fx/passes/reinplace.py
    torch/fx/passes/runtime_assert.py
    torch/fx/passes/shape_prop.py
    torch/fx/passes/splitter_base.py
    torch/fx/passes/split_module.py
    torch/fx/passes/split_utils.py
    torch/fx/passes/tools_common.py
    torch/fx/passes/_tensorify_python_scalars.py
    torch/fx/passes/__init__.py
    torch/fx/config.py
    torch/fx/graph.py
    torch/fx/graph_module.py
    torch/fx/immutable_collections.py
    torch/fx/interpreter.py
    torch/fx/node.py
    torch/fx/operator_schemas.py
    torch/fx/proxy.py
    torch/fx/subgraph_rewriter.py
    torch/fx/traceback.py
    torch/fx/_compatibility.py
    torch/fx/_lazy_graph_module.py
    torch/fx/_pytree.py
    torch/fx/_symbolic_trace.py
    torch/fx/_utils.py
    torch/fx/__init__.py
    torch/jit/annotations.py
    torch/jit/frontend.py
    torch/jit/_async.py
    torch/jit/_await.py
    torch/jit/_builtins.py
    torch/jit/_check.py
    torch/jit/_dataclass_impls.py
    torch/jit/_decomposition_utils.py
    torch/jit/_freeze.py
    torch/jit/_fuser.py
    torch/jit/_ir_utils.py
    torch/jit/_monkeytype_config.py
    torch/jit/_recursive.py
    torch/jit/_script.py
    torch/jit/_serialization.py
    torch/jit/_state.py
    torch/jit/_trace.py
    torch/jit/__init__.py
    torch/linalg/__init__.py
    torch/masked/maskedtensor/binary.py
    torch/masked/maskedtensor/core.py
    torch/masked/maskedtensor/creation.py
    torch/masked/maskedtensor/passthrough.py
    torch/masked/maskedtensor/reductions.py
    torch/masked/maskedtensor/unary.py
    torch/masked/maskedtensor/__init__.py
    torch/masked/_docs.py
    torch/masked/_ops.py
    torch/masked/__init__.py
    torch/monitor/__init__.py
    torch/mps/event.py
    torch/mps/profiler.py
    torch/mps/__init__.py
    torch/mtia/memory.py
    torch/mtia/_utils.py
    torch/mtia/__init__.py
    torch/multiprocessing/reductions.py
    torch/multiprocessing/spawn.py
    torch/multiprocessing/_atfork.py
    torch/multiprocessing/__init__.py
    torch/nested/_internal/nested_int.py
    torch/nested/_internal/nested_tensor.py
    torch/nested/_internal/__init__.py
    torch/nested/__init__.py
    torch/nn/attention/__init__.py
    torch/nn/intrinsic/modules/fused.py
    torch/nn/intrinsic/modules/__init__.py
    torch/nn/intrinsic/qat/modules/conv_fused.py
    torch/nn/intrinsic/qat/modules/linear_fused.py
    torch/nn/intrinsic/qat/modules/linear_relu.py
    torch/nn/intrinsic/qat/modules/__init__.py
    torch/nn/intrinsic/qat/__init__.py
    torch/nn/intrinsic/quantized/dynamic/modules/linear_relu.py
    torch/nn/intrinsic/quantized/dynamic/modules/__init__.py
    torch/nn/intrinsic/quantized/dynamic/__init__.py
    torch/nn/intrinsic/quantized/modules/bn_relu.py
    torch/nn/intrinsic/quantized/modules/conv_relu.py
    torch/nn/intrinsic/quantized/modules/linear_relu.py
    torch/nn/intrinsic/quantized/modules/__init__.py
    torch/nn/intrinsic/quantized/__init__.py
    torch/nn/intrinsic/__init__.py
    torch/nn/modules/activation.py
    torch/nn/modules/adaptive.py
    torch/nn/modules/batchnorm.py
    torch/nn/modules/channelshuffle.py
    torch/nn/modules/container.py
    torch/nn/modules/conv.py
    torch/nn/modules/distance.py
    torch/nn/modules/dropout.py
    torch/nn/modules/flatten.py
    torch/nn/modules/fold.py
    torch/nn/modules/instancenorm.py
    torch/nn/modules/lazy.py
    torch/nn/modules/linear.py
    torch/nn/modules/loss.py
    torch/nn/modules/module.py
    torch/nn/modules/normalization.py
    torch/nn/modules/padding.py
    torch/nn/modules/pixelshuffle.py
    torch/nn/modules/pooling.py
    torch/nn/modules/rnn.py
    torch/nn/modules/sparse.py
    torch/nn/modules/transformer.py
    torch/nn/modules/upsampling.py
    torch/nn/modules/utils.py
    torch/nn/modules/_functions.py
    torch/nn/modules/__init__.py
    torch/nn/parallel/comm.py
    torch/nn/parallel/data_parallel.py
    torch/nn/parallel/distributed.py
    torch/nn/parallel/parallel_apply.py
    torch/nn/parallel/replicate.py
    torch/nn/parallel/scatter_gather.py
    torch/nn/parallel/_functions.py
    torch/nn/parallel/__init__.py
    torch/nn/qat/dynamic/modules/linear.py
    torch/nn/qat/dynamic/modules/__init__.py
    torch/nn/qat/dynamic/__init__.py
    torch/nn/qat/modules/conv.py
    torch/nn/qat/modules/embedding_ops.py
    torch/nn/qat/modules/linear.py
    torch/nn/qat/modules/__init__.py
    torch/nn/qat/__init__.py
    torch/nn/quantizable/modules/__init__.py
    torch/nn/quantizable/__init__.py
    torch/nn/quantized/dynamic/__init__.py
    torch/nn/quantized/modules/__init__.py
    torch/nn/quantized/functional.py
    torch/nn/quantized/__init__.py
    torch/nn/utils/clip_grad.py
    torch/nn/utils/convert_parameters.py
    torch/nn/utils/fusion.py
    torch/nn/utils/init.py
    torch/nn/utils/memory_format.py
    torch/nn/utils/parametrizations.py
    torch/nn/utils/parametrize.py
    torch/nn/utils/rnn.py
    torch/nn/utils/spectral_norm.py
    torch/nn/utils/stateless.py
    torch/nn/utils/weight_norm.py
    torch/nn/utils/_named_member_accessor.py
    torch/nn/utils/__init__.py
    torch/nn/common_types.py
    torch/nn/functional.py
    torch/nn/grad.py
    torch/nn/init.py
    torch/nn/parameter.py
    torch/nn/_reduction.py
    torch/nn/__init__.py
    torch/onnx/_internal/diagnostics/infra/sarif/version.py
    torch/onnx/_internal/diagnostics/infra/sarif/_address.py
    torch/onnx/_internal/diagnostics/infra/sarif/_artifact.py
    torch/onnx/_internal/diagnostics/infra/sarif/_artifact_change.py
    torch/onnx/_internal/diagnostics/infra/sarif/_artifact_content.py
    torch/onnx/_internal/diagnostics/infra/sarif/_artifact_location.py
    torch/onnx/_internal/diagnostics/infra/sarif/_attachment.py
    torch/onnx/_internal/diagnostics/infra/sarif/_code_flow.py
    torch/onnx/_internal/diagnostics/infra/sarif/_configuration_override.py
    torch/onnx/_internal/diagnostics/infra/sarif/_conversion.py
    torch/onnx/_internal/diagnostics/infra/sarif/_edge.py
    torch/onnx/_internal/diagnostics/infra/sarif/_edge_traversal.py
    torch/onnx/_internal/diagnostics/infra/sarif/_exception.py
    torch/onnx/_internal/diagnostics/infra/sarif/_external_properties.py
    torch/onnx/_internal/diagnostics/infra/sarif/_external_property_file_reference.py
    torch/onnx/_internal/diagnostics/infra/sarif/_external_property_file_references.py
    torch/onnx/_internal/diagnostics/infra/sarif/_fix.py
    torch/onnx/_internal/diagnostics/infra/sarif/_graph.py
    torch/onnx/_internal/diagnostics/infra/sarif/_graph_traversal.py
    torch/onnx/_internal/diagnostics/infra/sarif/_invocation.py
    torch/onnx/_internal/diagnostics/infra/sarif/_location.py
    torch/onnx/_internal/diagnostics/infra/sarif/_location_relationship.py
    torch/onnx/_internal/diagnostics/infra/sarif/_logical_location.py
    torch/onnx/_internal/diagnostics/infra/sarif/_message.py
    torch/onnx/_internal/diagnostics/infra/sarif/_multiformat_message_string.py
    torch/onnx/_internal/diagnostics/infra/sarif/_node.py
    torch/onnx/_internal/diagnostics/infra/sarif/_notification.py
    torch/onnx/_internal/diagnostics/infra/sarif/_physical_location.py
    torch/onnx/_internal/diagnostics/infra/sarif/_property_bag.py
    torch/onnx/_internal/diagnostics/infra/sarif/_rectangle.py
    torch/onnx/_internal/diagnostics/infra/sarif/_region.py
    torch/onnx/_internal/diagnostics/infra/sarif/_replacement.py
    torch/onnx/_internal/diagnostics/infra/sarif/_reporting_configuration.py
    torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor.py
    torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor_reference.py
    torch/onnx/_internal/diagnostics/infra/sarif/_reporting_descriptor_relationship.py
    torch/onnx/_internal/diagnostics/infra/sarif/_result.py
    torch/onnx/_internal/diagnostics/infra/sarif/_result_provenance.py
    torch/onnx/_internal/diagnostics/infra/sarif/_run.py
    torch/onnx/_internal/diagnostics/infra/sarif/_run_automation_details.py
    torch/onnx/_internal/diagnostics/infra/sarif/_sarif_log.py
    torch/onnx/_internal/diagnostics/infra/sarif/_special_locations.py
    torch/onnx/_internal/diagnostics/infra/sarif/_stack.py
    torch/onnx/_internal/diagnostics/infra/sarif/_stack_frame.py
    torch/onnx/_internal/diagnostics/infra/sarif/_suppression.py
    torch/onnx/_internal/diagnostics/infra/sarif/_thread_flow.py
    torch/onnx/_internal/diagnostics/infra/sarif/_thread_flow_location.py
    torch/onnx/_internal/diagnostics/infra/sarif/_tool.py
    torch/onnx/_internal/diagnostics/infra/sarif/_tool_component.py
    torch/onnx/_internal/diagnostics/infra/sarif/_tool_component_reference.py
    torch/onnx/_internal/diagnostics/infra/sarif/_translation_metadata.py
    torch/onnx/_internal/diagnostics/infra/sarif/_version_control_details.py
    torch/onnx/_internal/diagnostics/infra/sarif/_web_request.py
    torch/onnx/_internal/diagnostics/infra/sarif/_web_response.py
    torch/onnx/_internal/diagnostics/infra/sarif/__init__.py
    torch/onnx/_internal/diagnostics/infra/context.py
    torch/onnx/_internal/diagnostics/infra/formatter.py
    torch/onnx/_internal/diagnostics/infra/utils.py
    torch/onnx/_internal/diagnostics/infra/_infra.py
    torch/onnx/_internal/diagnostics/infra/__init__.py
    torch/onnx/_internal/diagnostics/_diagnostic.py
    torch/onnx/_internal/diagnostics/_rules.py
    torch/onnx/_internal/diagnostics/__init__.py
    torch/onnx/_internal/exporter/_onnx_program.py
    torch/onnx/_internal/exporter/__init__.py
    torch/onnx/_internal/fx/decomposition_table.py
    torch/onnx/_internal/fx/patcher.py
    torch/onnx/_internal/fx/registration.py
    torch/onnx/_internal/fx/serialization.py
    torch/onnx/_internal/fx/__init__.py
    torch/onnx/_internal/io_adapter.py
    torch/onnx/_internal/jit_utils.py
    torch/onnx/_internal/onnxruntime.py
    torch/onnx/_internal/onnx_proto_utils.py
    torch/onnx/_internal/registration.py
    torch/onnx/_internal/_exporter_legacy.py
    torch/onnx/_internal/_lazy_import.py
    torch/onnx/_internal/__init__.py
    torch/onnx/errors.py
    torch/onnx/operators.py
    torch/onnx/symbolic_caffe2.py
    torch/onnx/symbolic_helper.py
    torch/onnx/symbolic_opset10.py
    torch/onnx/symbolic_opset11.py
    torch/onnx/symbolic_opset12.py
    torch/onnx/symbolic_opset13.py
    torch/onnx/symbolic_opset14.py
    torch/onnx/symbolic_opset15.py
    torch/onnx/symbolic_opset16.py
    torch/onnx/symbolic_opset17.py
    torch/onnx/symbolic_opset18.py
    torch/onnx/symbolic_opset19.py
    torch/onnx/symbolic_opset20.py
    torch/onnx/symbolic_opset7.py
    torch/onnx/symbolic_opset8.py
    torch/onnx/symbolic_opset9.py
    torch/onnx/utils.py
    torch/onnx/_constants.py
    torch/onnx/_deprecation.py
    torch/onnx/_globals.py
    torch/onnx/_type_utils.py
    torch/onnx/__init__.py
    torch/optim/adadelta.py
    torch/optim/adagrad.py
    torch/optim/adam.py
    torch/optim/adamax.py
    torch/optim/adamw.py
    torch/optim/asgd.py
    torch/optim/lbfgs.py
    torch/optim/lr_scheduler.py
    torch/optim/nadam.py
    torch/optim/optimizer.py
    torch/optim/radam.py
    torch/optim/rmsprop.py
    torch/optim/rprop.py
    torch/optim/sgd.py
    torch/optim/sparse_adam.py
    torch/optim/swa_utils.py
    torch/optim/_adafactor.py
    torch/optim/_functional.py
    torch/optim/__init__.py
    torch/package/analyze/find_first_use_of_broken_modules.py
    torch/package/analyze/is_from_package.py
    torch/package/analyze/trace_dependencies.py
    torch/package/analyze/__init__.py
    torch/package/file_structure_representation.py
    torch/package/find_file_dependencies.py
    torch/package/glob_group.py
    torch/package/importer.py
    torch/package/package_exporter.py
    torch/package/package_importer.py
    torch/package/_digraph.py
    torch/package/_directory_reader.py
    torch/package/_importlib.py
    torch/package/_mangling.py
    torch/package/_package_pickler.py
    torch/package/_package_unpickler.py
    torch/package/_stdlib.py
    torch/package/__init__.py
    torch/profiler/itt.py
    torch/profiler/profiler.py
    torch/profiler/_memory_profiler.py
    torch/profiler/_utils.py
    torch/profiler/__init__.py
    torch/quantization/fake_quantize.py
    torch/quantization/fuser_method_mappings.py
    torch/quantization/fuse_modules.py
    torch/quantization/observer.py
    torch/quantization/qconfig.py
    torch/quantization/quantization_mappings.py
    torch/quantization/quantize.py
    torch/quantization/quantize_jit.py
    torch/quantization/quant_type.py
    torch/quantization/stubs.py
    torch/quantization/__init__.py
    torch/signal/windows/windows.py
    torch/signal/windows/__init__.py
    torch/signal/__init__.py
    torch/sparse/semi_structured.py
    torch/sparse/_semi_structured_conversions.py
    torch/sparse/_semi_structured_ops.py
    torch/sparse/__init__.py
    torch/special/__init__.py
    torch/testing/_internal/distributed/fake_pg.py
    torch/testing/_internal/distributed/__init__.py
    torch/testing/_internal/composite_compliance.py
    torch/testing/_internal/logging_tensor.py
    torch/testing/_internal/__init__.py
    torch/testing/_comparison.py
    torch/testing/_creation.py
    torch/testing/_utils.py
    torch/testing/__init__.py
    torch/utils/backcompat/__init__.py
    torch/utils/data/datapipes/dataframe/dataframes.py
    torch/utils/data/datapipes/dataframe/dataframe_wrapper.py
    torch/utils/data/datapipes/dataframe/datapipes.py
    torch/utils/data/datapipes/dataframe/structures.py
    torch/utils/data/datapipes/dataframe/__init__.py
    torch/utils/data/datapipes/iter/callable.py
    torch/utils/data/datapipes/iter/combinatorics.py
    torch/utils/data/datapipes/iter/combining.py
    torch/utils/data/datapipes/iter/filelister.py
    torch/utils/data/datapipes/iter/fileopener.py
    torch/utils/data/datapipes/iter/grouping.py
    torch/utils/data/datapipes/iter/routeddecoder.py
    torch/utils/data/datapipes/iter/selecting.py
    torch/utils/data/datapipes/iter/sharding.py
    torch/utils/data/datapipes/iter/streamreader.py
    torch/utils/data/datapipes/iter/utils.py
    torch/utils/data/datapipes/iter/__init__.py
    torch/utils/data/datapipes/map/callable.py
    torch/utils/data/datapipes/map/combinatorics.py
    torch/utils/data/datapipes/map/combining.py
    torch/utils/data/datapipes/map/grouping.py
    torch/utils/data/datapipes/map/utils.py
    torch/utils/data/datapipes/map/__init__.py
    torch/utils/data/datapipes/utils/common.py
    torch/utils/data/datapipes/utils/decoder.py
    torch/utils/data/datapipes/utils/__init__.py
    torch/utils/data/datapipes/datapipe.py
    torch/utils/data/datapipes/_decorator.py
    torch/utils/data/datapipes/_hook_iterator.py
    torch/utils/data/datapipes/_typing.py
    torch/utils/data/datapipes/__init__.py
    torch/utils/data/_utils/collate.py
    torch/utils/data/_utils/fetch.py
    torch/utils/data/_utils/pin_memory.py
    torch/utils/data/_utils/signal_handling.py
    torch/utils/data/_utils/worker.py
    torch/utils/data/_utils/__init__.py
    torch/utils/data/dataloader.py
    torch/utils/data/dataset.py
    torch/utils/data/distributed.py
    torch/utils/data/graph.py
    torch/utils/data/graph_settings.py
    torch/utils/data/sampler.py
    torch/utils/data/__init__.py
    torch/utils/_sympy/functions.py
    torch/utils/_sympy/interp.py
    torch/utils/_sympy/numbers.py
    torch/utils/_sympy/printers.py
    torch/utils/_sympy/reference.py
    torch/utils/_sympy/singleton_int.py
    torch/utils/_sympy/solve.py
    torch/utils/_sympy/symbol.py
    torch/utils/_sympy/value_ranges.py
    torch/utils/_sympy/__init__.py
    torch/utils/backend_registration.py
    torch/utils/checkpoint.py
    torch/utils/collect_env.py
    torch/utils/cpp_backtrace.py
    torch/utils/deterministic.py
    torch/utils/dlpack.py
    torch/utils/flop_counter.py
    torch/utils/hooks.py
    torch/utils/model_zoo.py
    torch/utils/module_tracker.py
    torch/utils/throughput_benchmark.py
    torch/utils/weak.py
    torch/utils/_backport_slots.py
    torch/utils/_config_module.py
    torch/utils/_content_store.py
    torch/utils/_contextlib.py
    torch/utils/_cxx_pytree.py
    torch/utils/_device.py
    torch/utils/_exposed_in.py
    torch/utils/_foreach_utils.py
    torch/utils/_import_utils.py
    torch/utils/_mode_utils.py
    torch/utils/_ordered_set.py
    torch/utils/_python_dispatch.py
    torch/utils/_pytree.py
    torch/utils/_stats.py
    torch/utils/_thunk.py
    torch/utils/_traceback.py
    torch/utils/_triton.py
    torch/utils/_typing_utils.py
    torch/utils/__init__.py
    torch/xpu/memory.py
    torch/xpu/random.py
    torch/xpu/streams.py
    torch/xpu/_utils.py
    torch/xpu/__init__.py
    torch/_awaits/__init__.py
    torch/_custom_op/autograd.py
    torch/_custom_op/impl.py
    torch/_custom_op/__init__.py
    torch/_decomp/decompositions.py
    torch/_decomp/decompositions_for_rng.py
    torch/_decomp/__init__.py
    torch/_dispatch/python.py
    torch/_dispatch/__init__.py
    torch/_dynamo/backends/registry.py
    torch/_dynamo/backends/__init__.py
    torch/_dynamo/polyfills/builtins.py
    torch/_dynamo/polyfills/functools.py
    torch/_dynamo/polyfills/itertools.py
    torch/_dynamo/polyfills/loader.py
    torch/_dynamo/polyfills/operator.py
    torch/_dynamo/polyfills/os.py
    torch/_dynamo/polyfills/pytree.py
    torch/_dynamo/polyfills/sys.py
    torch/_dynamo/polyfills/__init__.py
    torch/_dynamo/variables/base.py
    torch/_dynamo/variables/builder.py
    torch/_dynamo/variables/builtin.py
    torch/_dynamo/variables/constant.py
    torch/_dynamo/variables/ctx_manager.py
    torch/_dynamo/variables/dicts.py
    torch/_dynamo/variables/distributed.py
    torch/_dynamo/variables/functions.py
    torch/_dynamo/variables/higher_order_ops.py
    torch/_dynamo/variables/iter.py
    torch/_dynamo/variables/lazy.py
    torch/_dynamo/variables/lists.py
    torch/_dynamo/variables/misc.py
    torch/_dynamo/variables/nn_module.py
    torch/_dynamo/variables/optimizer.py
    torch/_dynamo/variables/script_object.py
    torch/_dynamo/variables/sdpa.py
    torch/_dynamo/variables/tensor.py
    torch/_dynamo/variables/torch.py
    torch/_dynamo/variables/torch_function.py
    torch/_dynamo/variables/user_defined.py
    torch/_dynamo/variables/__init__.py
    torch/_dynamo/bytecode_analysis.py
    torch/_dynamo/bytecode_transformation.py
    torch/_dynamo/cache_size.py
    torch/_dynamo/callback.py
    torch/_dynamo/codegen.py
    torch/_dynamo/code_context.py
    torch/_dynamo/compiled_autograd.py
    torch/_dynamo/comptime.py
    torch/_dynamo/config.py
    torch/_dynamo/convert_frame.py
    torch/_dynamo/create_parameter_op.py
    torch/_dynamo/current_scope_id.py
    torch/_dynamo/decorators.py
    torch/_dynamo/device_interface.py
    torch/_dynamo/distributed.py
    torch/_dynamo/eval_frame.py
    torch/_dynamo/exc.py
    torch/_dynamo/external_utils.py
    torch/_dynamo/funcname_cache.py
    torch/_dynamo/graph_deduplication.py
    torch/_dynamo/graph_region_tracker.py
    torch/_dynamo/guards.py
    torch/_dynamo/hooks.py
    torch/_dynamo/logging.py
    torch/_dynamo/metrics_context.py
    torch/_dynamo/mutation_guard.py
    torch/_dynamo/output_graph.py
    torch/_dynamo/pgo.py
    torch/_dynamo/replay_record.py
    torch/_dynamo/resume_execution.py
    torch/_dynamo/side_effects.py
    torch/_dynamo/source.py
    torch/_dynamo/symbolic_convert.py
    torch/_dynamo/trace_rules.py
    torch/_dynamo/types.py
    torch/_dynamo/utils.py
    torch/_dynamo/_trace_wrapped_higher_order_op.py
    torch/_dynamo/__init__.py
    torch/_export/utils.py
    torch/_export/verifier.py
    torch/_export/wrappers.py
    torch/_export/__init__.py
    torch/_functorch/_activation_checkpointing/knapsack.py
    torch/_functorch/_activation_checkpointing/__init__.py
    torch/_functorch/_aot_autograd/autograd_cache.py
    torch/_functorch/_aot_autograd/collect_metadata_analysis.py
    torch/_functorch/_aot_autograd/dispatch_and_compile_graph.py
    torch/_functorch/_aot_autograd/functional_utils.py
    torch/_functorch/_aot_autograd/input_output_analysis.py
    torch/_functorch/_aot_autograd/jit_compile_runtime_wrappers.py
    torch/_functorch/_aot_autograd/logging_utils.py
    torch/_functorch/_aot_autograd/runtime_wrappers.py
    torch/_functorch/_aot_autograd/schemas.py
    torch/_functorch/_aot_autograd/subclass_utils.py
    torch/_functorch/_aot_autograd/traced_function_transforms.py
    torch/_functorch/_aot_autograd/utils.py
    torch/_functorch/_aot_autograd/__init__.py
    torch/_functorch/aot_autograd.py
    torch/_functorch/apis.py
    torch/_functorch/autograd_function.py
    torch/_functorch/batch_norm_replacement.py
    torch/_functorch/compile_utils.py
    torch/_functorch/config.py
    torch/_functorch/deprecated.py
    torch/_functorch/eager_transforms.py
    torch/_functorch/functional_call.py
    torch/_functorch/make_functional.py
    torch/_functorch/partitioners.py
    torch/_functorch/pyfunctorch.py
    torch/_functorch/utils.py
    torch/_functorch/vmap.py
    torch/_functorch/__init__.py
    torch/_higher_order_ops/cond.py
    torch/_higher_order_ops/effects.py
    torch/_higher_order_ops/flex_attention.py
    torch/_higher_order_ops/hints_wrap.py
    torch/_higher_order_ops/invoke_subgraph.py
    torch/_higher_order_ops/map.py
    torch/_higher_order_ops/out_dtype.py
    torch/_higher_order_ops/prim_hop_base.py
    torch/_higher_order_ops/scan.py
    torch/_higher_order_ops/strict_mode.py
    torch/_higher_order_ops/torchbind.py
    torch/_higher_order_ops/triton_kernel_wrap.py
    torch/_higher_order_ops/utils.py
    torch/_higher_order_ops/while_loop.py
    torch/_higher_order_ops/__init__.py
    torch/_inductor/codegen/cuda/cuda_env.py
    torch/_inductor/codegen/cuda/__init__.py
    torch/_inductor/codegen/rocm/compile_command.py
    torch/_inductor/codegen/rocm/__init__.py
    torch/_inductor/codegen/__init__.py
    torch/_inductor/runtime/autotune_cache.py
    torch/_inductor/runtime/cache_dir_utils.py
    torch/_inductor/runtime/compile_tasks.py
    torch/_inductor/runtime/hints.py
    torch/_inductor/runtime/runtime_utils.py
    torch/_inductor/runtime/__init__.py
    torch/_inductor/codecache.py
    torch/_inductor/config.py
    torch/_inductor/cpp_builder.py
    torch/_inductor/cpu_vec_isa.py
    torch/_inductor/cudagraph_utils.py
    torch/_inductor/custom_graph_pass.py
    torch/_inductor/exc.py
    torch/_inductor/inductor_prims.py
    torch/_inductor/metrics.py
    torch/_inductor/output_code.py
    torch/_inductor/remote_cache.py
    torch/_inductor/test_operators.py
    torch/_inductor/triton_bundler.py
    torch/_inductor/utils.py
    torch/_inductor/__init__.py
    torch/_library/autograd.py
    torch/_library/custom_ops.py
    torch/_library/fake_class_registry.py
    torch/_library/fake_impl.py
    torch/_library/infer_schema.py
    torch/_library/simple_registry.py
    torch/_library/triton.py
    torch/_library/utils.py
    torch/_library/__init__.py
    torch/_logging/structured.py
    torch/_logging/_internal.py
    torch/_logging/_registrations.py
    torch/_logging/__init__.py
    torch/_numpy/fft.py
    torch/_numpy/linalg.py
    torch/_numpy/random.py
    torch/_numpy/_binary_ufuncs_impl.py
    torch/_numpy/_casting_dicts.py
    torch/_numpy/_dtypes.py
    torch/_numpy/_dtypes_impl.py
    torch/_numpy/_funcs.py
    torch/_numpy/_funcs_impl.py
    torch/_numpy/_getlimits.py
    torch/_numpy/_ndarray.py
    torch/_numpy/_normalizations.py
    torch/_numpy/_reductions_impl.py
    torch/_numpy/_ufuncs.py
    torch/_numpy/_unary_ufuncs_impl.py
    torch/_numpy/_util.py
    torch/_numpy/__init__.py
    torch/_prims/context.py
    torch/_prims/debug_prims.py
    torch/_prims/executor.py
    torch/_prims/rng_prims.py
    torch/_prims/__init__.py
    torch/_prims_common/wrappers.py
    torch/_prims_common/__init__.py
    torch/_refs/linalg/__init__.py
    torch/_refs/nn/functional/__init__.py
    torch/_refs/nn/__init__.py
    torch/_refs/special/__init__.py
    torch/_refs/fft.py
    torch/_refs/_conversions.py
    torch/_refs/__init__.py
    torch/_strobelight/cli_function_profiler.py
    torch/_strobelight/compile_time_profiler.py
    torch/_strobelight/__init__.py
    torch/_subclasses/fake_impls.py
    torch/_subclasses/fake_tensor.py
    torch/_subclasses/fake_utils.py
    torch/_subclasses/functional_tensor.py
    torch/_subclasses/meta_utils.py
    torch/_subclasses/schema_check_mode.py # Required by printing tensors
    torch/_subclasses/_fake_tensor_utils.py
    torch/_subclasses/__init__.py
    torch/_vendor/packaging/version.py
    torch/_vendor/packaging/_structures.py
    torch/_vendor/packaging/__init__.py
    torch/_vendor/__init__.py
    torch/functional.py
    torch/hub.py
    torch/library.py
    torch/overrides.py
    torch/quasirandom.py
    torch/random.py
    torch/return_types.py
    torch/serialization.py
    torch/storage.py
    torch/torch_version.py
    torch/types.py
    torch/version.py
    torch/_classes.py
    torch/_compile.py
    torch/_custom_ops.py
    torch/_environment.py
    torch/_guards.py
    torch/_jit_internal.py
    torch/_linalg_utils.py
    torch/_lobpcg.py
    torch/_lowrank.py
    torch/_meta_registrations.py
    torch/_namedtensor_internals.py
    torch/_ops.py
    torch/_size_docs.py
    torch/_sources.py
    torch/_storage_docs.py
    torch/_tensor.py
    torch/_tensor_docs.py
    torch/_tensor_str.py
    torch/_torch_docs.py
    torch/_utils.py
    torch/_utils_internal.py
    torch/_VF.py
    torch/_vmap_internals.py
    torch/_weights_only_unpickler.py
    torch/__config__.py
    torch/__future__.py
    torch/__init__.py
    torchgen/code_template.py
    torchgen/model.py
    torchgen/utils.py
    torchgen/__init__.py
    torchvision/datasets/caltech.py
    torchvision/datasets/celeba.py
    torchvision/datasets/cifar.py
    torchvision/datasets/cityscapes.py
    torchvision/datasets/clevr.py
    torchvision/datasets/coco.py
    torchvision/datasets/country211.py
    torchvision/datasets/dtd.py
    torchvision/datasets/eurosat.py
    torchvision/datasets/fakedata.py
    torchvision/datasets/fer2013.py
    torchvision/datasets/fgvc_aircraft.py
    torchvision/datasets/flickr.py
    torchvision/datasets/flowers102.py
    torchvision/datasets/folder.py
    torchvision/datasets/food101.py
    torchvision/datasets/gtsrb.py
    torchvision/datasets/hmdb51.py
    torchvision/datasets/imagenet.py
    torchvision/datasets/imagenette.py
    torchvision/datasets/inaturalist.py
    torchvision/datasets/kinetics.py
    torchvision/datasets/kitti.py
    torchvision/datasets/lfw.py
    torchvision/datasets/lsun.py
    torchvision/datasets/mnist.py
    torchvision/datasets/moving_mnist.py
    torchvision/datasets/omniglot.py
    torchvision/datasets/oxford_iiit_pet.py
    torchvision/datasets/pcam.py
    torchvision/datasets/phototour.py
    torchvision/datasets/places365.py
    torchvision/datasets/rendered_sst2.py
    torchvision/datasets/sbd.py
    torchvision/datasets/sbu.py
    torchvision/datasets/semeion.py
    torchvision/datasets/stanford_cars.py
    torchvision/datasets/stl10.py
    torchvision/datasets/sun397.py
    torchvision/datasets/svhn.py
    torchvision/datasets/ucf101.py
    torchvision/datasets/usps.py
    torchvision/datasets/utils.py
    torchvision/datasets/video_utils.py
    torchvision/datasets/vision.py
    torchvision/datasets/voc.py
    torchvision/datasets/widerface.py
    torchvision/datasets/_optical_flow.py
    torchvision/datasets/_stereo_matching.py
    torchvision/datasets/__init__.py
    torchvision/io/image.py
    torchvision/io/video.py
    torchvision/io/video_reader.py
    torchvision/io/_load_gpu_decoder.py
    torchvision/io/_video_opt.py
    torchvision/io/__init__.py
    torchvision/models/detection/anchor_utils.py
    torchvision/models/detection/backbone_utils.py
    torchvision/models/detection/faster_rcnn.py
    torchvision/models/detection/fcos.py
    torchvision/models/detection/generalized_rcnn.py
    torchvision/models/detection/image_list.py
    torchvision/models/detection/keypoint_rcnn.py
    torchvision/models/detection/mask_rcnn.py
    torchvision/models/detection/retinanet.py
    torchvision/models/detection/roi_heads.py
    torchvision/models/detection/rpn.py
    torchvision/models/detection/ssd.py
    torchvision/models/detection/ssdlite.py
    torchvision/models/detection/transform.py
    torchvision/models/detection/_utils.py
    torchvision/models/detection/__init__.py
    torchvision/models/optical_flow/raft.py
    torchvision/models/optical_flow/_utils.py
    torchvision/models/optical_flow/__init__.py
    torchvision/models/quantization/googlenet.py
    torchvision/models/quantization/inception.py
    torchvision/models/quantization/mobilenet.py
    torchvision/models/quantization/mobilenetv2.py
    torchvision/models/quantization/mobilenetv3.py
    torchvision/models/quantization/resnet.py
    torchvision/models/quantization/shufflenetv2.py
    torchvision/models/quantization/utils.py
    torchvision/models/quantization/__init__.py
    torchvision/models/segmentation/deeplabv3.py
    torchvision/models/segmentation/fcn.py
    torchvision/models/segmentation/lraspp.py
    torchvision/models/segmentation/_utils.py
    torchvision/models/segmentation/__init__.py
    torchvision/models/video/mvit.py
    torchvision/models/video/resnet.py
    torchvision/models/video/s3d.py
    torchvision/models/video/swin_transformer.py
    torchvision/models/video/__init__.py
    torchvision/models/alexnet.py
    torchvision/models/convnext.py
    torchvision/models/densenet.py
    torchvision/models/efficientnet.py
    torchvision/models/googlenet.py
    torchvision/models/inception.py
    torchvision/models/maxvit.py
    torchvision/models/mnasnet.py
    torchvision/models/mobilenet.py
    torchvision/models/mobilenetv2.py
    torchvision/models/mobilenetv3.py
    torchvision/models/regnet.py
    torchvision/models/resnet.py
    torchvision/models/shufflenetv2.py
    torchvision/models/squeezenet.py
    torchvision/models/swin_transformer.py
    torchvision/models/vgg.py
    torchvision/models/vision_transformer.py
    torchvision/models/_api.py
    torchvision/models/_meta.py
    torchvision/models/_utils.py
    torchvision/models/__init__.py
    torchvision/ops/boxes.py
    torchvision/ops/ciou_loss.py
    torchvision/ops/deform_conv.py
    torchvision/ops/diou_loss.py
    torchvision/ops/drop_block.py
    torchvision/ops/feature_pyramid_network.py
    torchvision/ops/focal_loss.py
    torchvision/ops/giou_loss.py
    torchvision/ops/misc.py
    torchvision/ops/poolers.py
    torchvision/ops/ps_roi_align.py
    torchvision/ops/ps_roi_pool.py
    torchvision/ops/roi_align.py
    torchvision/ops/roi_pool.py
    torchvision/ops/stochastic_depth.py
    torchvision/ops/_box_convert.py
    torchvision/ops/_register_onnx_ops.py
    torchvision/ops/_utils.py
    torchvision/ops/__init__.py
    torchvision/transforms/autoaugment.py
    torchvision/transforms/functional.py
    torchvision/transforms/transforms.py
    torchvision/transforms/_functional_pil.py
    torchvision/transforms/_functional_tensor.py
    torchvision/transforms/_presets.py
    torchvision/transforms/__init__.py
    torchvision/extension.py
    torchvision/utils.py
    torchvision/version.py
    torchvision/_internally_replaced_utils.py
    torchvision/_meta_registrations.py
    torchvision/_utils.py
    torchvision/__init__.py
    tqdm/cli.py
    tqdm/gui.py
    tqdm/std.py
    tqdm/utils.py
    tqdm/version.py
    tqdm/_dist_ver.py
    tqdm/_monitor.py
    tqdm/_tqdm_pandas.py
    tqdm/__init__.py
    xformers/ops/fmha/attn_bias.py
    xformers/ops/fmha/ck.py
    xformers/ops/fmha/ck_decoder.py
    xformers/ops/fmha/ck_splitk.py
    xformers/ops/fmha/common.py
    xformers/ops/fmha/cutlass.py
    xformers/ops/fmha/dispatch.py
    xformers/ops/fmha/flash.py
    xformers/ops/fmha/flash3.py
    xformers/ops/fmha/torch_attention_compat.py
    xformers/ops/fmha/triton_splitk.py
    xformers/ops/fmha/__init__.py
    xformers/ops/_triton/__init__.py
    xformers/ops/common.py
    xformers/ops/differentiable_collectives.py
    xformers/ops/indexing.py
    xformers/ops/ipc.py
    xformers/ops/modpar_layers.py
    xformers/ops/rmsnorm.py
    xformers/ops/rope_padded.py
    xformers/ops/seqpar.py
    xformers/ops/sequence_parallel_fused_ops.py
    xformers/ops/sp24.py
    xformers/ops/swiglu_op.py
    xformers/ops/tiled_matmul.py
    xformers/ops/unbind.py
    xformers/ops/__init__.py
    xformers/checkpoint.py
    xformers/version.py
    xformers/_cpp_lib.py
    xformers/__init__.py
    typing_extensions.py
)
if(WIN32)
    set(py_files ${py_files}
        win32/lib/pywintypes.py
        win32/lib/win32con.py
        win32/lib/winerror.py
    )
endif()
foreach(src_file ${py_files})
    set(src_file "Lib/site-packages/${src_file}")
    get_filename_component(src_file_dir ${src_file} DIRECTORY)
    get_filename_component(src_file_stem ${src_file} NAME_WE)
    set(pyc_file "${src_file_dir}/${src_file_stem}.pyc")
    set(dst_file "${aihi_output_dir}/Python/${pyc_file}")
    add_custom_command(OUTPUT ${dst_file}
        COMMAND ${Python3_EXECUTABLE} -m compileall -b -o $<IF:$<CONFIG:Debug>,0,1> PythonVenv/${src_file}
        COMMAND ${CMAKE_COMMAND} -E copy "PythonVenv/${pyc_file}" ${dst_file}
        COMMENT "Deploying ${src_file} ..."
        DEPENDS PythonVenv/${src_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        VERBATIM COMMAND_EXPAND_LISTS
    )

    list(APPEND deployed_files ${dst_file})
endforeach()

set(py_suffix cp${Python3_VERSION_MAJOR}${Python3_VERSION_MINOR}-win_amd64.pyd)

set(copy_files
    cumm/core_cc.${py_suffix}
    lark/grammars/common.lark
    numpy/_core/_multiarray_umath.${py_suffix}
    numpy/linalg/_umath_linalg.${py_suffix}
    numpy/fft/_pocketfft_umath.${py_suffix}
    numpy/random/_bounded_integers.${py_suffix}
    numpy/random/_common.${py_suffix}
    numpy/random/_generator.${py_suffix}
    numpy/random/_mt19937.${py_suffix}
    numpy/random/_pcg64.${py_suffix}
    numpy/random/_philox.${py_suffix}
    numpy/random/_sfc64.${py_suffix}
    numpy/random/bit_generator.${py_suffix}
    numpy/random/mtrand.${py_suffix}
    numpy.libs/libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll
    numpy.libs/msvcp140-d64049c6e3865410a7dda6a7e9f0c575.dll
    onnx/onnx_cpp2py_export.${py_suffix}
    PIL/_imaging.${py_suffix}
    safetensors/_safetensors_rust.${py_suffix}
    spconv/core_cc.${py_suffix}
    torch/_C.${py_suffix}
    torch/functional.py
    torch/_dynamo/config.py
    torch/_inductor/config.py
    torch/_functorch/config.py
    torch/compiler/config.py
    torch/fx/experimental/_config.py
    torch/lib/asmjit.dll
    torch/lib/c10.dll
    torch/lib/c10_cuda.dll
    torch/lib/cublas64_12.dll
    torch/lib/cublasLt64_12.dll
    torch/lib/cudart64_12.dll
    torch/lib/cudnn64_9.dll
    torch/lib/cudnn_engines_precompiled64_9.dll
    torch/lib/cudnn_engines_runtime_compiled64_9.dll
    torch/lib/cudnn_graph64_9.dll
    torch/lib/cudnn_heuristic64_9.dll
    torch/lib/cudnn_ops64_9.dll
    torch/lib/cufft64_11.dll
    torch/lib/cusolver64_11.dll
    torch/lib/cusparse64_12.dll
    torch/lib/fbgemm.dll
    torch/lib/libiomp5md.dll
    torch/lib/nvJitLink_120_0.dll
    torch/lib/nvToolsExt64_1.dll
    torch/lib/shm.dll
    torch/lib/torch_cpu.dll
    torch/lib/torch_cuda.dll
    torch/lib/torch_python.dll
    torch/lib/uv.dll
    torch/nn/functional.py
    torch/nn/modules/rnn.py
    torchvision/_C.pyd
    torchvision/image.pyd
    torchvision/jpeg8.dll
    torchvision/libpng16.dll
    torchvision/nvjpeg64_12.dll
    torchvision/zlib.dll
    torchvision/models/densenet.py
    xformers/_C.pyd
    xformers/_C_flashattention.pyd
    xformers/cpp_lib.json
)
if(WIN32)
    set(copy_files ${copy_files}
        pywin32_system32/pywintypes${Python3_VERSION_MAJOR}${Python3_VERSION_MINOR}.dll
        win32/win32file.pyd
        win32/_win32sysloader.pyd
    )
endif()
foreach(src_file ${copy_files})
    set(src_file "Lib/site-packages/${src_file}")
    set(dst_file "${aihi_output_dir}/Python/${src_file}")
    add_custom_command(OUTPUT ${dst_file}
        COMMAND ${CMAKE_COMMAND} -E create_hardlink "PythonVenv/${src_file}" ${dst_file}
        COMMENT "Deploying ${src_file} ..."
        DEPENDS PythonVenv/${src_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        VERBATIM COMMAND_EXPAND_LISTS
    )

    list(APPEND deployed_files ${dst_file})
endforeach()

set(py_files
    asyncio/base_events.py
    asyncio/base_futures.py
    asyncio/base_subprocess.py
    asyncio/base_tasks.py
    asyncio/constants.py
    asyncio/coroutines.py
    asyncio/events.py
    asyncio/exceptions.py
    asyncio/format_helpers.py
    asyncio/futures.py
    asyncio/locks.py
    asyncio/log.py
    asyncio/mixins.py
    asyncio/proactor_events.py
    asyncio/protocols.py
    asyncio/queues.py
    asyncio/runners.py
    asyncio/selector_events.py
    asyncio/sslproto.py
    asyncio/staggered.py
    asyncio/streams.py
    asyncio/subprocess.py
    asyncio/taskgroups.py
    asyncio/tasks.py
    asyncio/threads.py
    asyncio/timeouts.py
    asyncio/transports.py
    asyncio/trsock.py
    asyncio/windows_events.py
    asyncio/windows_utils.py
    asyncio/__init__.py
    collections/abc.py
    collections/__init__.py
    concurrent/futures/thread.py
    concurrent/futures/_base.py
    concurrent/futures/__init__.py
    concurrent/__init__.py
    ctypes/wintypes.py
    ctypes/_endian.py
    ctypes/__init__.py
    email/base64mime.py
    email/charset.py
    email/encoders.py
    email/errors.py
    email/feedparser.py
    email/header.py
    email/iterators.py
    email/message.py
    email/parser.py
    email/quoprimime.py
    email/utils.py
    email/_encoded_words.py
    email/_parseaddr.py
    email/_policybase.py
    email/__init__.py
    encodings/aliases.py
    encodings/gbk.py
    encodings/raw_unicode_escape.py
    encodings/unicode_escape.py
    encodings/utf_8.py
    encodings/__init__.py
    html/entities.py
    html/parser.py
    html/__init__.py
    http/client.py
    http/__init__.py
    importlib/metadata/_adapters.py
    importlib/metadata/_collections.py
    importlib/metadata/_functools.py
    importlib/metadata/_itertools.py
    importlib/metadata/_meta.py
    importlib/metadata/_text.py
    importlib/metadata/__init__.py
    importlib/resources/abc.py
    importlib/resources/_adapters.py
    importlib/resources/_common.py
    importlib/resources/_legacy.py
    importlib/resources/__init__.py
    importlib/abc.py
    importlib/_abc.py
    importlib/__init__.py
    json/decoder.py
    json/encoder.py
    json/scanner.py
    json/__init__.py
    logging/__init__.py
    multiprocessing/connection.py
    multiprocessing/context.py
    multiprocessing/process.py
    multiprocessing/reduction.py
    multiprocessing/resource_sharer.py
    multiprocessing/synchronize.py
    multiprocessing/util.py
    multiprocessing/__init__.py
    re/_casefix.py
    re/_compiler.py
    re/_constants.py
    re/_parser.py
    re/__init__.py
    unittest/case.py
    unittest/loader.py
    unittest/main.py
    unittest/mock.py
    unittest/result.py
    unittest/runner.py
    unittest/signals.py
    unittest/suite.py
    unittest/util.py
    unittest/__init__.py
    urllib/error.py
    urllib/parse.py
    urllib/request.py
    urllib/response.py
    urllib/__init__.py
    xml/etree/ElementPath.py
    xml/etree/ElementTree.py
    xml/etree/__init__.py
    xml/__init__.py
    argparse.py
    ast.py
    base64.py
    bdb.py
    bisect.py
    bz2.py
    calendar.py
    cmd.py
    code.py
    codeop.py
    colorsys.py
    contextlib.py
    contextvars.py
    copy.py
    copyreg.py
    cProfile.py
    csv.py
    dataclasses.py
    datetime.py
    decimal.py
    difflib.py
    dis.py
    enum.py
    fnmatch.py
    fractions.py
    functools.py
    getpass.py
    gettext.py
    glob.py
    gzip.py
    hashlib.py
    heapq.py
    hmac.py
    inspect.py
    ipaddress.py
    keyword.py
    linecache.py
    locale.py
    lzma.py
    mimetypes.py
    modulefinder.py
    nturl2path.py
    numbers.py
    opcode.py
    operator.py
    pathlib.py
    pdb.py
    pickle.py
    pickletools.py
    pkgutil.py
    platform.py
    pprint.py
    profile.py
    pstats.py
    queue.py
    quopri.py
    random.py
    reprlib.py
    secrets.py
    selectors.py
    shlex.py
    shutil.py
    signal.py
    socket.py
    ssl.py
    string.py
    struct.py
    subprocess.py
    sysconfig.py
    tarfile.py
    tempfile.py
    textwrap.py
    threading.py
    timeit.py
    token.py
    tokenize.py
    traceback.py
    types.py
    typing.py
    uuid.py
    warnings.py
    weakref.py
    _compat_pickle.py
    _compression.py
    _markupbase.py
    _weakrefset.py
    __future__.py
)
if(Python3_VERSION VERSION_GREATER_EQUAL "3.12")
    set(py_files ${py_files}
        zipfile/_path/glob.py
        zipfile/_path/__init__.py
        zipfile/__init__.py
    )
else()
    set(py_files ${py_files}
        zipfile.py
    )
endif()
foreach(src_file ${py_files})
    get_filename_component(src_file_dir ${src_file} DIRECTORY)
    file(COPY "${Python3_STDLIB}/${src_file}" DESTINATION "${CMAKE_CURRENT_BINARY_DIR}/Python/Lib/${src_file_dir}")
endforeach()
foreach(src_file ${py_files})
    set(src_file "Python/Lib/${src_file}")
    get_filename_component(src_file_dir ${src_file} DIRECTORY)
    get_filename_component(src_file_stem ${src_file} NAME_WE)
    set(pyc_file "${src_file_dir}/${src_file_stem}.pyc")
    set(dst_file "${aihi_output_dir}/${pyc_file}")
    add_custom_command(OUTPUT ${dst_file}
        COMMAND ${Python3_EXECUTABLE} -m compileall -b -o $<IF:$<CONFIG:Debug>,0,1> ${src_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${pyc_file} ${dst_file}
        COMMENT "Deploying ${src_file} ..."
        DEPENDS ${src_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        VERBATIM COMMAND_EXPAND_LISTS
    )

    list(APPEND deployed_files ${dst_file})
endforeach()

set(copy_files
    _asyncio.pyd
    _bz2.pyd
    _ctypes.pyd
    _decimal.pyd
    _elementtree.pyd
    _hashlib.pyd
    _lzma.pyd
    _multiprocessing.pyd
    _overlapped.pyd
    _queue.pyd
    _socket.pyd
    _ssl.pyd
    _uuid.pyd
    libcrypto-3.dll
    libffi-8.dll
    libssl-3.dll
    pyexpat.pyd
    select.pyd
    unicodedata.pyd
)
foreach(src_file ${copy_files})
    set(src_file "DLLs/${src_file}")
    get_filename_component(src_file_dir ${src_file} DIRECTORY)
    file(COPY "${Python3_RUNTIME_LIBRARY_DIRS}/${src_file}" DESTINATION "${CMAKE_CURRENT_BINARY_DIR}/Python/${src_file_dir}")
endforeach()
foreach(src_file ${copy_files})
    set(src_file "Python/DLLs/${src_file}")
    set(dst_file "${aihi_output_dir}/${src_file}")
    add_custom_command(OUTPUT ${dst_file}
        COMMAND ${CMAKE_COMMAND} -E create_hardlink ${src_file} ${dst_file}
        COMMENT "Deploying ${src_file} ..."
        DEPENDS ${src_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        VERBATIM COMMAND_EXPAND_LISTS
    )

    list(APPEND deployed_files ${dst_file})
endforeach()

set(copy_files
    python${Python3_VERSION_MAJOR}${Python3_VERSION_MINOR}.dll
    python3.dll
)
foreach(src_file ${copy_files})
    get_filename_component(src_file_dir ${src_file} DIRECTORY)
    file(COPY "${Python3_RUNTIME_LIBRARY_DIRS}/${src_file}" DESTINATION "${CMAKE_CURRENT_BINARY_DIR}/Python/${src_file_dir}")
endforeach()
foreach(src_file ${copy_files})
    set(dst_file "${aihi_output_dir}/${src_file}")
    add_custom_command(OUTPUT ${dst_file}
        COMMAND ${CMAKE_COMMAND} -E create_hardlink "Python/${src_file}" ${dst_file}
        COMMENT "Deploying ${src_file} ..."
        DEPENDS Python/${src_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        VERBATIM COMMAND_EXPAND_LISTS
    )

    list(APPEND deployed_files ${dst_file})
endforeach()

add_custom_target(DeployPython DEPENDS ${deployed_files} requirements.txt)

target_sources(DeployPython
    PRIVATE
        requirements.txt
)

set_target_properties(DeployPython PROPERTIES FOLDER "External")
