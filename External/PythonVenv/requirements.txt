numpy==2.2.2

protobuf==5.28.3
#numpy==2.2.2
onnx==1.17.0

--extra-index-url https://download.pytorch.org/whl/cu124

mpmath==1.3.0
typing-extensions==4.12.2
sympy==1.13.1
networkx==3.4.2
MarkupSafe==3.0.2
fsspec==2024.10.0
filelock==3.16.1
jinja2==3.1.6
torch==2.6.0+cu124

#mpmath==1.3.0
#typing-extensions==4.12.2
#sympy==1.13.1
pillow==11.0.0
#numpy==2.2.2
#networkx==3.4.2
#MarkupSafe==3.0.2
#fsspec==2024.10.0
#filelock==3.16.1
#jinja2==3.1.6
#torch==2.6.0+cu124
torchvision==0.21.0+cu124

#mpmath==1.3.0
#typing-extensions==4.12.2
#sympy==1.13.1
#pillow==11.0.0
#numpy==2.2.2
#networkx==3.4.2
#MarkupSafe==3.0.2
#fsspec==2024.10.0
#filelock==3.16.1
#onnx==1.16.2
#jinja2==3.1.6
#torch==2.6.0+cu124
#torchvision==0.19.1+cu124
onnx2torch==1.5.15

#mpmath==1.3.0
#typing-extensions==4.12.2
#sympy==1.13.1
#numpy==2.2.2
#networkx==3.4.2
#MarkupSafe==3.0.2
#fsspec==2024.10.0
#filelock==3.16.1
#jinja2==3.1.6
#torch==2.6.0+cu124
xformers==0.0.29.post2

safetensors==0.4.5

colorama==0.4.6
tqdm==4.67.0

pywin32==308; sys_platform == 'win32'
#mpmath==1.3.0
urllib3==2.2.3
termcolor==2.5.0
#sympy==1.13.1
pybind11==2.13.6
portalocker==3.1.1
#numpy==2.2.2
ninja==1.11.1.3
lark==1.2.2
idna==3.10
charset-normalizer==3.4.0
certifi==2024.8.30
requests==2.32.3
fire==0.7.0
ccimport==0.4.4
pccm==0.4.16
cumm-cu124==0.7.11
spconv-cu124==2.3.8
