if(WIN32)
    if(MSVC AND (CMAKE_GENERATOR MATCHES "^Visual Studio"))
        if((CMAKE_GENERATOR_PLATFORM STREQUAL "x64") OR (CMAKE_GENERATOR MATCHES "Win64") OR (CMAKE_GENERATOR_PLATFORM STREQUAL ""))
            set(ai_holo_imager_arch_name "x64")
        else()
            message(FATAL_ERROR "This CPU architecture is not supported")
        endif()
    endif()
    set(ai_holo_imager_platform_name "win")
    set(ai_holo_imager_platform_windows TRUE)
endif()

if(${CMAKE_HOST_SYSTEM_NAME} STREQUAL "Windows")
    set(ai_holo_imager_host_platform_name "win")
    set(ai_holo_imager_host_platform_windows TRUE)
endif()

if(NOT ai_holo_imager_arch_name)
    if((CMAKE_SYSTEM_PROCESSOR MATCHES "AMD64") OR (CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64"))
        set(ai_holo_imager_arch_name "x64")
    else()
        set(ai_holo_imager_arch_name "x86")
    endif()
endif()

if((CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "AMD64") OR (CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64"))
    set(ai_holo_imager_host_arch_name "x64")
else()
    set(ai_holo_imager_host_arch_name "x86")
endif()

set(ai_holo_imager_platform_name ${ai_holo_imager_platform_name}_${ai_holo_imager_arch_name})
set(ai_holo_imager_host_platform_name ${ai_holo_imager_host_platform_name}_${ai_holo_imager_host_arch_name})
