AccessModifierOffset: -4
AlignAfterOpenBracket: DontAlign
AlignEscapedNewlines: Left
AllowAllParametersOfDeclarationOnNextLine: false
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: None
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AlwaysBreakTemplateDeclarations: true
BreakBeforeBraces: Custom
BraceWrapping:
    AfterCaseLabel: true
    AfterClass: true
    AfterControlStatement: true
    AfterEnum: true
    AfterFunction: true
    AfterNamespace: true
    AfterStruct: true
    AfterUnion: true
    AfterExternBlock: true
    BeforeCatch: true
    BeforeElse: true
    IndentBraces: false
    SplitEmptyFunction: true
    SplitEmptyRecord: true
    SplitEmptyNamespace: true
BreakConstructorInitializers: BeforeColon
ColumnLimit: 140
ConstructorInitializerAllOnOneLineOrOnePerLine: false
Cpp11BracedListStyle: true
IndentPPDirectives: BeforeHash
IndentWidth: 4
Language: Cpp
MaxEmptyLinesToKeep: 2
NamespaceIndentation: All
PointerAlignment: Left
SpaceBeforeParens: ControlStatements
Standard: Cpp11
TabWidth: 4
UseTab: Never
