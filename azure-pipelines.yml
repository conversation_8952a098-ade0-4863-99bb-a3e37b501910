stages:
- stage: CodeFormatting
  jobs:
  - job: CodeFormatting
    pool:
      vmImage: Ubuntu-22.04

    steps:
    - script: |
        find . -iname "*.hpp" -o -iname "*.cpp" | xargs clang-format -i
        git diff --exit-code $(Build.SourceVersion)
      failOnStderr: true
      displayName: 'Check code formatting'

- stage: Build
  condition: succeeded('CodeFormatting')
  jobs:
  - job: Build

    strategy:
      matrix:
        Windows_vc143_Debug:
          configuration: Debug
        Windows_vc143_Release:
          configuration: Release

    pool:
      vmImage: windows-2022

    steps:
      - checkout: self
        fetchDepth: 1

      - bash: |
          choco install ninja
        displayName: 'Install'

      - bash: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Dummy Name"
        displayName: 'Config git'

      - script: |
          call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvarsall.bat" amd64
          cmake -B Build -G Ninja . -DCMAKE_BUILD_TYPE=$(configuration) -DCMAKE_C_COMPILER=cl -DCMAKE_CXX_COMPILER=cl
          cmake --build Build -j
        displayName: 'Build'
